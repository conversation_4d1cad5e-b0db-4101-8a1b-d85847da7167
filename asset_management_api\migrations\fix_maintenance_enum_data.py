#!/usr/bin/env python3
"""
Migration script to fix maintenance table enum data inconsistencies.

This script fixes:
1. Priority values that don't match the enum (case sensitivity)
2. Category values that don't match the enum
3. Ensures all data conforms to the model enums

Run this script to fix the existing data before the enum validation errors.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from sqlalchemy import create_engine, text
from app.config.database import DATABASE_URL
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def fix_maintenance_enum_data():
    """Fix maintenance table enum data inconsistencies."""
    
    try:
        # Create engine
        engine = create_engine(DATABASE_URL)
        
        with engine.connect() as connection:
            logger.info("🔧 Starting maintenance data fix...")
            
            # Start transaction
            trans = connection.begin()
            
            try:
                # Check current data
                logger.info("📊 Checking current maintenance data...")
                
                # Check priorities
                priority_result = connection.execute(text("""
                    SELECT DISTINCT priority, COUNT(*) as count 
                    FROM maintenance 
                    GROUP BY priority
                """))
                
                logger.info("Current priority values:")
                for row in priority_result:
                    logger.info(f"  - '{row.priority}': {row.count} records")
                
                # Check categories  
                category_result = connection.execute(text("""
                    SELECT DISTINCT category, COUNT(*) as count 
                    FROM maintenance 
                    GROUP BY category
                """))
                
                logger.info("Current category values:")
                for row in category_result:
                    logger.info(f"  - '{row.category}': {row.count} records")
                
                # Fix priority values - normalize to lowercase
                logger.info("🔄 Fixing priority values...")
                
                priority_fixes = [
                    ("HIGH", "high"),
                    ("LOW", "low"), 
                    ("MEDIUM", "medium"),
                    ("CRITICAL", "critical"),
                    ("High", "high"),
                    ("Low", "low"),
                    ("Medium", "medium"),
                    ("Critical", "critical")
                ]
                
                for old_val, new_val in priority_fixes:
                    result = connection.execute(text("""
                        UPDATE maintenance 
                        SET priority = :new_val 
                        WHERE priority = :old_val
                    """), {"old_val": old_val, "new_val": new_val})
                    
                    if result.rowcount > 0:
                        logger.info(f"  ✅ Fixed {result.rowcount} records: '{old_val}' → '{new_val}'")
                
                # Fix category values - normalize to lowercase
                logger.info("🔄 Fixing category values...")
                
                category_fixes = [
                    ("AV COMPUTERS", "AV computers"),
                    ("AV computers", "AV computers"), # Ensure correct case
                    ("AV_COMPUTERS", "AV computers"),
                    ("AV PRINTERS", "AV printers"),
                    ("AV_PRINTERS", "AV printers"),
                    ("TABS", "tabs"),
                    ("OTHERS", "others"),
                    ("SCANNERS", "scanners"),
                    ("POLLPADS", "pollpads"),
                    ("pollpads", "pollpads"), # Keep as is
                    ("MONITORS", "monitors"),
                    ("Monitors", "monitors"),
                    ("Others", "others"),
                    ("Tabs", "tabs"),
                    ("Scanners", "scanners")
                ]
                
                for old_val, new_val in category_fixes:
                    result = connection.execute(text("""
                        UPDATE maintenance 
                        SET category = :new_val 
                        WHERE category = :old_val
                    """), {"old_val": old_val, "new_val": new_val})
                    
                    if result.rowcount > 0:
                        logger.info(f"  ✅ Fixed {result.rowcount} records: '{old_val}' → '{new_val}'")
                
                # Handle any category values that don't match known values
                logger.info("🔍 Checking for unknown category values...")
                
                valid_categories = [
                    "AV computers", "AV printers", "tabs", "others", 
                    "scanners", "pollpads", "monitors"
                ]
                
                # Use string formatting for IN clause to avoid tuple conversion issues
                placeholders = ",".join([f"'{cat}'" for cat in valid_categories])
                unknown_categories = connection.execute(text(f"""
                    SELECT DISTINCT category, COUNT(*) as count 
                    FROM maintenance 
                    WHERE category NOT IN ({placeholders})
                    GROUP BY category
                """))
                
                for row in unknown_categories:
                    logger.warning(f"  ⚠️  Unknown category '{row.category}': {row.count} records")
                    # You might want to map these to 'others' or handle them specifically
                    connection.execute(text("""
                        UPDATE maintenance 
                        SET category = 'others' 
                        WHERE category = :unknown_cat
                    """), {"unknown_cat": row.category})
                    logger.info(f"  ✅ Mapped '{row.category}' to 'others'")
                
                # Verify the fixes
                logger.info("✅ Verifying fixes...")
                
                final_priorities = connection.execute(text("""
                    SELECT DISTINCT priority, COUNT(*) as count 
                    FROM maintenance 
                    GROUP BY priority
                """))
                
                logger.info("Final priority values:")
                for row in final_priorities:
                    logger.info(f"  - '{row.priority}': {row.count} records")
                
                final_categories = connection.execute(text("""
                    SELECT DISTINCT category, COUNT(*) as count 
                    FROM maintenance 
                    GROUP BY category
                """))
                
                logger.info("Final category values:")
                for row in final_categories:
                    logger.info(f"  - '{row.category}': {row.count} records")
                
                # Commit the transaction
                trans.commit()
                logger.info("✅ All maintenance data fixes completed successfully!")
                
            except Exception as e:
                # Rollback on error
                trans.rollback()
                logger.error(f"❌ Error during migration: {e}")
                raise
                
    except Exception as e:
        logger.error(f"❌ Failed to connect to database: {e}")
        raise

if __name__ == "__main__":
    fix_maintenance_enum_data() 