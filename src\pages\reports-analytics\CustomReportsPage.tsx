import { AppLayout } from "@/components/layout/AppLayout";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { useState, useEffect } from "react";
import {
  Filter,
  Play,
  Save,
  Calendar,
  FileText,
  BarChart3,
  Table,
  Settings,
  Search,
  Loader2,
  AlertCircle
} from "lucide-react";
import { ReportActions } from "@/components/reports/ReportActions";

interface SavedReport {
  id: string;
  name: string;
  description: string;
  reportType: string;
  filters: any;
  createdAt: string;
  lastRun: string;
  format: string;
}

export default function CustomReportsPage() {
  const [reportName, setReportName] = useState("");
  const [selectedAssetTypes, setSelectedAssetTypes] = useState<string[]>([]);
  const [selectedStatuses, setSelectedStatuses] = useState<string[]>([]);
  const [selectedCounties, setSelectedCounties] = useState<string[]>([]);

  // Backend data states
  const [assetTypes, setAssetTypes] = useState<string[]>([]);
  const [statuses, setStatuses] = useState<string[]>([]);
  const [counties, setCounties] = useState<string[]>([]);
  const [savedReports, setSavedReports] = useState<SavedReport[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const exportFormats = ["PDF", "Excel", "CSV", "JSON"];

  useEffect(() => {
    fetchReportData();
  }, []);

  const fetchReportData = async () => {
    try {
      setLoading(true);
      setError(null);

      // Fetch asset types, statuses, counties, and saved reports from backend
      const [assetTypesRes, statusesRes, countiesRes, savedReportsRes] = await Promise.all([
        fetch('http://localhost:8000/api/asset-types', {
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('token')}`,
            'Content-Type': 'application/json',
          },
        }),
        fetch('http://localhost:8000/api/asset-status', {
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('token')}`,
            'Content-Type': 'application/json',
          },
        }),
        fetch('http://localhost:8000/api/masters/locations', {
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('token')}`,
            'Content-Type': 'application/json',
          },
        }),
        fetch('http://localhost:8000/api/reports/custom', {
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('token')}`,
            'Content-Type': 'application/json',
          },
        })
      ]);

      if (assetTypesRes.ok) {
        const assetTypesData = await assetTypesRes.json();
        setAssetTypes(assetTypesData.data?.map((type: any) => type.name) || []);
      }

      if (statusesRes.ok) {
        const statusesData = await statusesRes.json();
        setStatuses(statusesData.data?.map((status: any) => status.name) || []);
      }

      if (countiesRes.ok) {
        const countiesData = await countiesRes.json();
        setCounties(countiesData.data?.map((county: any) => county.name) || []);
      }

      if (savedReportsRes.ok) {
        const savedReportsData = await savedReportsRes.json();
        setSavedReports(savedReportsData.data || []);
      }

    } catch (err) {
      console.error('Error fetching report data:', err);
      setError(err instanceof Error ? err.message : 'Failed to load report data');
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <AppLayout>
        <div className="flex items-center justify-center h-64">
          <Loader2 className="h-8 w-8 animate-spin" />
          <span className="ml-2">Loading report data...</span>
        </div>
      </AppLayout>
    );
  }

  if (error) {
    return (
      <AppLayout>
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <AlertCircle className="h-8 w-8 text-red-500 mx-auto mb-4" />
            <p className="text-red-600 mb-4">{error}</p>
            <Button onClick={fetchReportData}>Retry</Button>
          </div>
        </div>
      </AppLayout>
    );
  }

  return (
    <AppLayout>
      <div className="space-y-6 animate-fade-in p-6">
        <div className="flex flex-col md:flex-row md:items-center justify-between gap-4">
          <div>
            <h1 className="text-2xl font-bold text-zenith-blue-dark mb-2">Custom Reports</h1>
            <p className="text-muted-foreground">
              Create detailed, filterable reports tailored to specific requirements
            </p>
          </div>
          <div className="flex gap-2">
            <Button variant="outline" className="flex items-center gap-2">
              <Search className="h-4 w-4" />
              Browse Templates
            </Button>
          </div>
        </div>

        <Tabs defaultValue="create" className="w-full">
          <TabsList>
            <TabsTrigger value="create">Create Report</TabsTrigger>
            <TabsTrigger value="saved">Saved Reports</TabsTrigger>
            <TabsTrigger value="templates">Templates</TabsTrigger>
          </TabsList>

          <TabsContent value="create" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              
              {/* Report Configuration */}
              <Card className="lg:col-span-2">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Settings className="h-5 w-5" />
                    Report Configuration
                  </CardTitle>
                  <CardDescription>Configure your custom report parameters</CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  
                  {/* Basic Info */}
                  <div className="space-y-4">
                    <div>
                      <Label htmlFor="reportName">Report Name</Label>
                      <Input
                        id="reportName"
                        placeholder="Enter report name"
                        value={reportName}
                        onChange={(e) => setReportName(e.target.value)}
                      />
                    </div>
                    
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <Label>Report Type</Label>
                        <Select>
                          <SelectTrigger>
                            <SelectValue placeholder="Select type" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="inventory">Inventory Report</SelectItem>
                            <SelectItem value="performance">Performance Analysis</SelectItem>
                            <SelectItem value="maintenance">Maintenance Report</SelectItem>
                            <SelectItem value="utilization">Utilization Report</SelectItem>
                            <SelectItem value="compliance">Compliance Report</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                      
                      <div>
                        <Label>Date Range</Label>
                        <Select>
                          <SelectTrigger>
                            <SelectValue placeholder="Select range" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="today">Today</SelectItem>
                            <SelectItem value="week">Last 7 days</SelectItem>
                            <SelectItem value="month">Last 30 days</SelectItem>
                            <SelectItem value="quarter">Last 3 months</SelectItem>
                            <SelectItem value="year">Last 12 months</SelectItem>
                            <SelectItem value="custom">Custom Range</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>
                  </div>

                  {/* Filters */}
                  <div className="space-y-4">
                    <h3 className="text-lg font-medium">Filters</h3>
                    
                    <div>
                      <Label>Asset Types</Label>
                      <div className="grid grid-cols-2 md:grid-cols-3 gap-2 mt-2">
                        {assetTypes.map((type) => (
                          <div key={type} className="flex items-center space-x-2">
                            <Checkbox
                              id={type}
                              checked={selectedAssetTypes.includes(type)}
                              onCheckedChange={(checked) => {
                                if (checked) {
                                  setSelectedAssetTypes([...selectedAssetTypes, type]);
                                } else {
                                  setSelectedAssetTypes(selectedAssetTypes.filter(t => t !== type));
                                }
                              }}
                            />
                            <Label htmlFor={type} className="text-sm">{type}</Label>
                          </div>
                        ))}
                      </div>
                    </div>

                    <div>
                      <Label>Asset Status</Label>
                      <div className="grid grid-cols-2 md:grid-cols-4 gap-2 mt-2">
                        {statuses.map((status) => (
                          <div key={status} className="flex items-center space-x-2">
                            <Checkbox
                              id={status}
                              checked={selectedStatuses.includes(status)}
                              onCheckedChange={(checked) => {
                                if (checked) {
                                  setSelectedStatuses([...selectedStatuses, status]);
                                } else {
                                  setSelectedStatuses(selectedStatuses.filter(s => s !== status));
                                }
                              }}
                            />
                            <Label htmlFor={status} className="text-sm">{status}</Label>
                          </div>
                        ))}
                      </div>
                    </div>

                    <div>
                      <Label>Counties</Label>
                      <div className="grid grid-cols-2 md:grid-cols-3 gap-2 mt-2">
                        {counties.map((county) => (
                          <div key={county} className="flex items-center space-x-2">
                            <Checkbox
                              id={county}
                              checked={selectedCounties.includes(county)}
                              onCheckedChange={(checked) => {
                                if (checked) {
                                  setSelectedCounties([...selectedCounties, county]);
                                } else {
                                  setSelectedCounties(selectedCounties.filter(c => c !== county));
                                }
                              }}
                            />
                            <Label htmlFor={county} className="text-sm">{county}</Label>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>

                  {/* Field Selection */}
                  <div className="space-y-4">
                    <h3 className="text-lg font-medium">Field Selection</h3>
                    <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
                      {["Asset ID", "Asset Type", "Status", "Location", "County", "Last Updated", "Condition", "Utilization Rate", "Maintenance History", "Cost", "Warranty Info", "Serial Number"].map((field) => (
                        <div key={field} className="flex items-center space-x-2">
                          <Checkbox id={field} />
                          <Label htmlFor={field} className="text-sm">{field}</Label>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Custom Date Ranges */}
                  <div className="space-y-4">
                    <h3 className="text-lg font-medium">Custom Date Ranges</h3>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <div>
                        <Label>Predefined Ranges</Label>
                        <Select>
                          <SelectTrigger>
                            <SelectValue placeholder="Select range" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="today">Today</SelectItem>
                            <SelectItem value="week">Last 7 days</SelectItem>
                            <SelectItem value="month">Last 30 days</SelectItem>
                            <SelectItem value="quarter">Last 3 months</SelectItem>
                            <SelectItem value="year">Last 12 months</SelectItem>
                            <SelectItem value="financial">Financial Year</SelectItem>
                            <SelectItem value="custom">Custom Range</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                      <div>
                        <Label>Start Date</Label>
                        <Input type="date" />
                      </div>
                      <div>
                        <Label>End Date</Label>
                        <Input type="date" />
                      </div>
                    </div>
                  </div>

                  {/* Multi-format Export Options */}
                  <div className="space-y-4">
                    <h3 className="text-lg font-medium">Multi-format Export Options</h3>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <Label>Primary Export Format</Label>
                        <Select>
                          <SelectTrigger>
                            <SelectValue placeholder="Select format" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="pdf">PDF - Professional Reports</SelectItem>
                            <SelectItem value="excel">Excel - Data Analysis</SelectItem>
                            <SelectItem value="csv">CSV - Raw Data</SelectItem>
                            <SelectItem value="word">Word - Document Format</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>

                      <div>
                        <Label>Chart/Visualization Type</Label>
                        <Select>
                          <SelectTrigger>
                            <SelectValue placeholder="Select chart" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="bar">Bar Chart</SelectItem>
                            <SelectItem value="pie">Pie Chart</SelectItem>
                            <SelectItem value="line">Line Chart</SelectItem>
                            <SelectItem value="table">Data Table</SelectItem>
                            <SelectItem value="mixed">Mixed Charts</SelectItem>
                            <SelectItem value="dashboard">Dashboard View</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>

                    <div className="space-y-2">
                      <Label>Additional Export Formats</Label>
                      <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
                        {["PDF", "Excel", "CSV", "Word"].map((format) => (
                          <div key={format} className="flex items-center space-x-2">
                            <Checkbox id={`export-${format}`} />
                            <Label htmlFor={`export-${format}`} className="text-sm">{format}</Label>
                          </div>
                        ))}
                      </div>
                      <p className="text-xs text-muted-foreground">
                        Select multiple formats to generate reports in different formats simultaneously for stakeholder sharing and analysis
                      </p>
                    </div>
                  </div>

                  {/* Action Buttons */}
                  <div className="flex gap-2 pt-4 border-t">
                    <Button className="flex items-center gap-2">
                      <Play className="h-4 w-4" />
                      Generate Report
                    </Button>
                    <Button variant="outline" className="flex items-center gap-2">
                      <Save className="h-4 w-4" />
                      Save Template
                    </Button>
                    <Button variant="outline" className="flex items-center gap-2">
                      <Filter className="h-4 w-4" />
                      Preview
                    </Button>
                  </div>
                </CardContent>
              </Card>

              {/* Quick Actions */}
              <Card>
                <CardHeader>
                  <CardTitle>Quick Actions</CardTitle>
                  <CardDescription>Common report types</CardDescription>
                </CardHeader>
                <CardContent className="space-y-3">
                  <Button variant="outline" className="w-full justify-start">
                    <BarChart3 className="h-4 w-4 mr-2" />
                    Asset Utilization
                  </Button>
                  <Button variant="outline" className="w-full justify-start">
                    <Table className="h-4 w-4 mr-2" />
                    Inventory Summary
                  </Button>
                  <Button variant="outline" className="w-full justify-start">
                    <Calendar className="h-4 w-4 mr-2" />
                    Maintenance Schedule
                  </Button>
                  <Button variant="outline" className="w-full justify-start">
                    <FileText className="h-4 w-4 mr-2" />
                    Compliance Report
                  </Button>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="saved" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Saved Reports</CardTitle>
                <CardDescription>Your previously created custom reports</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {savedReports.map((report) => (
                    <div key={report.id} className="flex items-center justify-between p-4 border rounded-lg">
                      <div className="flex items-center gap-4">
                        <FileText className="h-5 w-5 text-blue-600" />
                        <div>
                          <h3 className="font-medium">{report.name}</h3>
                          <p className="text-sm text-muted-foreground">
                            {report.type} • Last run: {report.lastRun}
                          </p>
                        </div>
                      </div>
                      <ReportActions 
                        onDownload={(format) => {
                          // In a real app, this would handle the download
                          console.log(`Downloading ${report.name} in ${format} format`);
                        }}
                        onPrint={() => {
                          // In a real app, this would trigger printing
                          console.log(`Printing ${report.name}`);
                          window.print();
                        }}
                      />
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="templates" className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              <Card className="cursor-pointer hover:shadow-md transition-shadow">
                <CardHeader>
                  <CardTitle className="text-lg">Asset Performance</CardTitle>
                  <CardDescription>Comprehensive asset utilization and performance metrics</CardDescription>
                </CardHeader>
                <CardContent>
                  <Button className="w-full">Use Template</Button>
                </CardContent>
              </Card>
              
              <Card className="cursor-pointer hover:shadow-md transition-shadow">
                <CardHeader>
                  <CardTitle className="text-lg">Maintenance Summary</CardTitle>
                  <CardDescription>Detailed maintenance schedules and completion rates</CardDescription>
                </CardHeader>
                <CardContent>
                  <Button className="w-full">Use Template</Button>
                </CardContent>
              </Card>
              
              <Card className="cursor-pointer hover:shadow-md transition-shadow">
                <CardHeader>
                  <CardTitle className="text-lg">County Comparison</CardTitle>
                  <CardDescription>Side-by-side county performance analysis</CardDescription>
                </CardHeader>
                <CardContent>
                  <Button className="w-full">Use Template</Button>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </AppLayout>
  );
}