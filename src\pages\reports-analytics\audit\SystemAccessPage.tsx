import React, { useState, useEffect } from 'react';
import { AppLayout } from "@/components/layout/AppLayout";
import { <PERSON>, Card<PERSON>ontent, Card<PERSON>eader, CardTitle, CardDescription } from "@/components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { DatePickerWithRange } from "@/components/ui/date-picker-with-range";
import { Shield, Download, AlertTriangle, Check, Loader2, AlertCircle, Clock, Info } from 'lucide-react';
import { ReportActions } from "@/components/reports/ReportActions";

interface SystemAccess {
  id: string;
  userId: string | null;
  userName: string | null;
  userEmail: string | null;
  attemptedEmail: string | null;
  accessType: 'Login' | 'Logout' | 'Failed Login' | 'Password Reset' | 'Permission Change' | 'Session Timeout';
  ipAddress: string;
  userAgent: string | null;
  status: 'success' | 'failed' | 'warning' | 'error';
  details: string | null;
  failureReason: string | null;
  loginTime: string | null;
  logoutTime: string | null;
  sessionDurationMinutes: number | null;
  timestamp: string;
}

interface PaginationInfo {
  total: number;
  limit: number;
  offset: number;
  hasMore: boolean;
}

// No mock data - using only backend data

export default function SystemAccessPage() {
  const [accessLogs, setAccessLogs] = useState<SystemAccess[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [accessTypeFilter, setAccessTypeFilter] = useState('all');
  const [dateRange, setDateRange] = useState<{ from: Date; to: Date } | undefined>();
  const [pagination, setPagination] = useState<PaginationInfo>({
    total: 0,
    limit: 50,
    offset: 0,
    hasMore: false
  });

  useEffect(() => {
    fetchAccessLogs();
  }, [statusFilter, accessTypeFilter, pagination.offset]);

  const fetchAccessLogs = async () => {
    try {
      setLoading(true);
      setError(null);

      const params = new URLSearchParams({
        limit: pagination.limit.toString(),
        offset: pagination.offset.toString(),
      });

      if (statusFilter !== 'all') {
        params.append('status', statusFilter);
      }

      if (accessTypeFilter !== 'all') {
        params.append('access_type', accessTypeFilter);
      }

      if (dateRange?.from) {
        params.append('start_date', dateRange.from.toISOString());
      }

      if (dateRange?.to) {
        params.append('end_date', dateRange.to.toISOString());
      }

      const response = await fetch(`http://localhost:8000/api/reports/audit/system-access?${params}`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error('Failed to fetch system access logs');
      }

      const result = await response.json();
      if (result.success) {
        setAccessLogs(result.data.accessLogs);
        setPagination(result.data.pagination);
      } else {
        throw new Error('Invalid response format');
      }
    } catch (err) {
      console.error('Error fetching system access logs:', err);
      setError(err instanceof Error ? err.message : 'Failed to load system access logs');
    } finally {
      setLoading(false);
    }
  };

  const handleLoadMore = () => {
    setPagination(prev => ({ ...prev, offset: prev.offset + prev.limit }));
  };

  // Filter logs locally for search term
  const filteredLogs = accessLogs.filter(log => {
    if (!searchTerm) return true;
    const searchLower = searchTerm.toLowerCase();
    return (
      log.userName?.toLowerCase().includes(searchLower) ||
      log.userEmail?.toLowerCase().includes(searchLower) ||
      log.ipAddress.toLowerCase().includes(searchLower) ||
      log.details?.toLowerCase().includes(searchLower)
    );
  });

  if (loading && accessLogs.length === 0) {
    return (
      <AppLayout>
        <div className="flex items-center justify-center h-64">
          <Loader2 className="h-8 w-8 animate-spin" />
          <span className="ml-2">Loading system access logs...</span>
        </div>
      </AppLayout>
    );
  }

  if (error) {
    return (
      <AppLayout>
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <AlertCircle className="h-8 w-8 text-red-500 mx-auto mb-4" />
            <p className="text-red-600 mb-4">{error}</p>
            <Button onClick={fetchAccessLogs}>Retry</Button>
          </div>
        </div>
      </AppLayout>
    );
  }

    if (searchTerm) {
      filtered = filtered.filter(log => 
        log.userName.toLowerCase().includes(searchTerm.toLowerCase()) ||
        log.ipAddress.includes(searchTerm) ||
        log.details.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    if (statusFilter !== 'all') {
      filtered = filtered.filter(log => log.status === statusFilter);
    }

    if (accessTypeFilter !== 'all') {
      filtered = filtered.filter(log => log.accessType === accessTypeFilter);
    }

    if (dateRange?.from && dateRange?.to) {
      filtered = filtered.filter(log => {
        const logDate = new Date(log.timestamp);
        return logDate >= dateRange.from && logDate <= dateRange.to;
      });
    }

    filtered.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());
    setFilteredLogs(filtered);
  }, [accessLogs, searchTerm, statusFilter, accessTypeFilter, dateRange]);

  const handleExport = () => {
    const csvContent = [
      ['User ID', 'User Name', 'Access Type', 'IP Address', 'User Agent', 'Timestamp', 'Status', 'Details'].join(','),
      ...filteredLogs.map(log => [
        log.userId,
        log.userName,
        log.accessType,
        log.ipAddress,
        log.userAgent,
        new Date(log.timestamp).toLocaleString(),
        log.status,
        log.details
      ].join(','))
    ].join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `system-access-logs-${new Date().toISOString().split('T')[0]}.csv`;
    a.click();
    window.URL.revokeObjectURL(url);
  };

  const getStatusIcon = (status: SystemAccess['status']) => {
    switch (status) {
      case 'Success':
        return <Check className="h-4 w-4 text-green-600" />;
      case 'Failed':
        return <AlertTriangle className="h-4 w-4 text-red-600" />;
      case 'Warning':
        return <AlertTriangle className="h-4 w-4 text-yellow-600" />;
    }
  };

  return (
    <AppLayout>
      <div className="container mx-auto py-6">
        <div className="mb-6">
          <h1 className="text-3xl font-bold">System Access Logs</h1>
          <p className="text-muted-foreground">Monitor and analyze system access attempts and security events</p>
        </div>

        <Card className="mb-6">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Shield className="h-5 w-5" />
              Access Log Analysis
            </CardTitle>
            <CardDescription>
              Track system access patterns and security events
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
              <Input
                placeholder="Search users, IPs, or details..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full"
              />
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger>
                  <SelectValue placeholder="Filter by status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Statuses</SelectItem>
                  <SelectItem value="Success">Success</SelectItem>
                  <SelectItem value="Failed">Failed</SelectItem>
                  <SelectItem value="Warning">Warning</SelectItem>
                </SelectContent>
              </Select>
              <Select value={accessTypeFilter} onValueChange={setAccessTypeFilter}>
                <SelectTrigger>
                  <SelectValue placeholder="Filter by access type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Types</SelectItem>
                  <SelectItem value="Login">Login</SelectItem>
                  <SelectItem value="Logout">Logout</SelectItem>
                  <SelectItem value="Failed Login">Failed Login</SelectItem>
                  <SelectItem value="Password Reset">Password Reset</SelectItem>
                  <SelectItem value="Permission Change">Permission Change</SelectItem>
                </SelectContent>
              </Select>
              <Button onClick={handleExport} className="flex items-center gap-2">
                <Download className="h-4 w-4" />
                Export Logs
              </Button>
            </div>

            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Status</TableHead>
                    <TableHead>User</TableHead>
                    <TableHead>Access Type</TableHead>
                    <TableHead>IP Address</TableHead>
                    <TableHead>Timestamp</TableHead>
                    <TableHead>Details</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredLogs.map((log) => (
                    <TableRow key={log.id}>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          {getStatusIcon(log.status)}
                          <span className={`text-sm font-medium ${
                            log.status === 'Success' ? 'text-green-600' :
                            log.status === 'Failed' ? 'text-red-600' :
                            'text-yellow-600'
                          }`}>
                            {log.status}
                          </span>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex flex-col">
                          <span className="font-medium">{log.userName}</span>
                          <span className="text-sm text-muted-foreground">{log.userId}</span>
                        </div>
                      </TableCell>
                      <TableCell>{log.accessType}</TableCell>
                      <TableCell className="font-mono">{log.ipAddress}</TableCell>
                      <TableCell>{new Date(log.timestamp).toLocaleString()}</TableCell>
                      <TableCell>{log.details}</TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          </CardContent>
        </Card>
      </div>
    </AppLayout>
  );
}
