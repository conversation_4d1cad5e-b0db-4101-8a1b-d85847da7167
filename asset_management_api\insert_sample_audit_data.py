"""
Insert sample audit data for testing Reports & Analytics functionality
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from sqlalchemy import create_engine, text
from app.config.database import D<PERSON><PERSON><PERSON>E_URL
from datetime import datetime, timedelta
import random
import json
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def insert_sample_audit_data():
    """Insert sample audit data for testing."""
    try:
        engine = create_engine(DATABASE_URL)
        
        with engine.connect() as conn:
            # Sample user activity logs
            user_activities = [
                {
                    'user_id': 'user-001',
                    'user_name': '<PERSON>',
                    'user_email': '<EMAIL>',
                    'activity_type': 'login',
                    'action': 'User logged into system',
                    'module': 'Authentication',
                    'resource_type': 'System',
                    'resource_id': None,
                    'resource_name': None,
                    'ip_address': '*************',
                    'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                    'status': 'success',
                    'details': 'Successful login from office network',
                    'created_at': datetime.now() - timedelta(hours=2)
                },
                {
                    'user_id': 'user-002',
                    'user_name': 'Sarah Johnson',
                    'user_email': '<EMAIL>',
                    'activity_type': 'create',
                    'action': 'Created new asset record',
                    'module': 'Asset Management',
                    'resource_type': 'Asset',
                    'resource_id': 'ASSET-001',
                    'resource_name': 'Poll Pad #001',
                    'ip_address': '*************',
                    'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                    'status': 'success',
                    'details': 'Created new Poll Pad asset record',
                    'created_at': datetime.now() - timedelta(hours=1)
                },
                {
                    'user_id': 'user-001',
                    'user_name': 'John Smith',
                    'user_email': '<EMAIL>',
                    'activity_type': 'export',
                    'action': 'Generated asset report',
                    'module': 'Reports',
                    'resource_type': 'Report',
                    'resource_id': 'RPT-001',
                    'resource_name': 'Asset Status Report',
                    'ip_address': '*************',
                    'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                    'status': 'success',
                    'details': 'Exported asset status report in PDF format',
                    'created_at': datetime.now() - timedelta(minutes=30)
                }
            ]
            
            for activity in user_activities:
                conn.execute(text("""
                    INSERT INTO user_activity_logs 
                    (user_id, user_name, user_email, activity_type, action, module, 
                     resource_type, resource_id, resource_name, ip_address, user_agent, 
                     status, details, created_at)
                    VALUES 
                    (:user_id, :user_name, :user_email, :activity_type, :action, :module,
                     :resource_type, :resource_id, :resource_name, :ip_address, :user_agent,
                     :status, :details, :created_at)
                """), activity)
            
            # Sample system access logs
            access_logs = [
                {
                    'user_id': 'user-001',
                    'user_name': 'John Smith',
                    'user_email': '<EMAIL>',
                    'attempted_email': None,
                    'access_type': 'Login',
                    'ip_address': '*************',
                    'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                    'status': 'success',
                    'details': 'Successful login',
                    'failure_reason': None,
                    'login_time': datetime.now() - timedelta(hours=2),
                    'logout_time': None,
                    'session_duration_minutes': None,
                    'created_at': datetime.now() - timedelta(hours=2)
                },
                {
                    'user_id': None,
                    'user_name': None,
                    'user_email': None,
                    'attempted_email': '<EMAIL>',
                    'access_type': 'Failed Login',
                    'ip_address': '************',
                    'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64)',
                    'status': 'failed',
                    'details': 'Invalid credentials',
                    'failure_reason': 'User not found',
                    'login_time': None,
                    'logout_time': None,
                    'session_duration_minutes': None,
                    'created_at': datetime.now() - timedelta(hours=3)
                },
                {
                    'user_id': 'user-002',
                    'user_name': 'Sarah Johnson',
                    'user_email': '<EMAIL>',
                    'attempted_email': None,
                    'access_type': 'Logout',
                    'ip_address': '*************',
                    'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                    'status': 'success',
                    'details': 'User logged out',
                    'failure_reason': None,
                    'login_time': None,
                    'logout_time': datetime.now() - timedelta(minutes=15),
                    'session_duration_minutes': 120,
                    'created_at': datetime.now() - timedelta(minutes=15)
                }
            ]
            
            for log in access_logs:
                conn.execute(text("""
                    INSERT INTO system_access_logs 
                    (user_id, user_name, user_email, attempted_email, access_type, ip_address, 
                     user_agent, status, details, failure_reason, login_time, logout_time, 
                     session_duration_minutes, created_at)
                    VALUES 
                    (:user_id, :user_name, :user_email, :attempted_email, :access_type, :ip_address,
                     :user_agent, :status, :details, :failure_reason, :login_time, :logout_time,
                     :session_duration_minutes, :created_at)
                """), log)
            
            # Sample asset scan logs
            scan_logs = [
                {
                    'asset_id': 1,
                    'asset_tag': 'POLL-001',
                    'asset_name': 'Poll Pad #001',
                    'asset_type': 'Poll Pad',
                    'scan_type': 'Check-in',
                    'location_name': 'Warehouse A',
                    'scanned_by_user_id': 'user-001',
                    'scanned_by_name': 'John Smith',
                    'status': 'success',
                    'is_valid_asset': True,
                    'validation_errors': None,
                    'notes': 'Asset checked in after election',
                    'scanned_at': datetime.now() - timedelta(hours=1),
                    'created_at': datetime.now() - timedelta(hours=1)
                },
                {
                    'asset_id': None,
                    'asset_tag': 'INVALID-999',
                    'asset_name': None,
                    'asset_type': None,
                    'scan_type': 'Inventory',
                    'location_name': 'Storage Room B',
                    'scanned_by_user_id': 'user-002',
                    'scanned_by_name': 'Sarah Johnson',
                    'status': 'failed',
                    'is_valid_asset': False,
                    'validation_errors': '["Asset not found in database"]',
                    'notes': 'Unknown asset tag scanned during inventory',
                    'scanned_at': datetime.now() - timedelta(minutes=45),
                    'created_at': datetime.now() - timedelta(minutes=45)
                }
            ]
            
            for log in scan_logs:
                conn.execute(text("""
                    INSERT INTO asset_scan_logs 
                    (asset_id, asset_tag, asset_name, asset_type, scan_type, location_name,
                     scanned_by_user_id, scanned_by_name, status, is_valid_asset, 
                     validation_errors, notes, scanned_at, created_at)
                    VALUES 
                    (:asset_id, :asset_tag, :asset_name, :asset_type, :scan_type, :location_name,
                     :scanned_by_user_id, :scanned_by_name, :status, :is_valid_asset,
                     :validation_errors, :notes, :scanned_at, :created_at)
                """), log)
            
            # Sample scheduled reports
            scheduled_reports = [
                {
                    'name': 'Daily Asset Status Report',
                    'description': 'Daily summary of asset status across all locations',
                    'report_type': 'asset_status',
                    'frequency': 'daily',
                    'time_of_day': '18:00',
                    'export_format': 'PDF',
                    'delivery_method': 'email',
                    'recipients': json.dumps(['<EMAIL>', '<EMAIL>']),
                    'status': 'active',
                    'is_enabled': True,
                    'last_run_at': datetime.now() - timedelta(days=1),
                    'next_run_at': datetime.now() + timedelta(hours=6),
                    'last_run_status': 'success',
                    'run_count': 30,
                    'success_count': 29,
                    'failure_count': 1,
                    'created_by_user_id': 'user-001',
                    'created_by_name': 'John Smith',
                    'created_at': datetime.now() - timedelta(days=30)
                },
                {
                    'name': 'Weekly Performance Summary',
                    'description': 'Weekly asset utilization and performance metrics',
                    'report_type': 'performance',
                    'frequency': 'weekly',
                    'time_of_day': '09:00',
                    'export_format': 'Excel',
                    'delivery_method': 'email',
                    'recipients': json.dumps(['<EMAIL>']),
                    'status': 'active',
                    'is_enabled': True,
                    'last_run_at': datetime.now() - timedelta(days=7),
                    'next_run_at': datetime.now() + timedelta(days=7),
                    'last_run_status': 'success',
                    'run_count': 12,
                    'success_count': 12,
                    'failure_count': 0,
                    'created_by_user_id': 'user-002',
                    'created_by_name': 'Sarah Johnson',
                    'created_at': datetime.now() - timedelta(days=90)
                }
            ]
            
            for report in scheduled_reports:
                conn.execute(text("""
                    INSERT INTO scheduled_reports 
                    (name, description, report_type, frequency, time_of_day, export_format,
                     delivery_method, recipients, status, is_enabled, last_run_at, next_run_at,
                     last_run_status, run_count, success_count, failure_count, 
                     created_by_user_id, created_by_name, created_at)
                    VALUES 
                    (:name, :description, :report_type, :frequency, :time_of_day, :export_format,
                     :delivery_method, :recipients, :status, :is_enabled, :last_run_at, :next_run_at,
                     :last_run_status, :run_count, :success_count, :failure_count,
                     :created_by_user_id, :created_by_name, :created_at)
                """), report)
            
            conn.commit()
            logger.info("Sample audit data inserted successfully!")
            
    except Exception as e:
        logger.error(f"Error inserting sample audit data: {e}")
        raise

if __name__ == "__main__":
    logger.info("Starting sample audit data insertion...")
    
    try:
        insert_sample_audit_data()
        logger.info("All operations completed successfully!")
        
    except Exception as e:
        logger.error(f"Script failed: {e}")
        sys.exit(1)
