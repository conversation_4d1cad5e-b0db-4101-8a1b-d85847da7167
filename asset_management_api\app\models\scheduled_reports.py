# app/models/scheduled_reports.py
from sqlalchemy import Column, Integer, String, Text, DateTime, Boolean, ForeignKey, Enum as SQLE<PERSON>, JSON
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
from app.config.database import Base
import enum

class ScheduleFrequency(enum.Enum):
    DAILY = "daily"
    WEEKLY = "weekly"
    MONTHLY = "monthly"
    QUARTERLY = "quarterly"
    YEARLY = "yearly"
    CUSTOM = "custom"

class ReportStatus(enum.Enum):
    ACTIVE = "active"
    PAUSED = "paused"
    DISABLED = "disabled"
    ERROR = "error"

class DeliveryMethod(enum.Enum):
    EMAIL = "email"
    DOWNLOAD = "download"
    API = "api"
    FTP = "ftp"

class ScheduledReport(Base):
    """
    Scheduled report configurations and management
    """
    __tablename__ = "scheduled_reports"

    id = Column(Integer, primary_key=True, autoincrement=True)
    
    # Report identification
    name = Column(String(255), nullable=False)
    description = Column(Text, nullable=True)
    report_type = Column(String(100), nullable=False)  # asset_status, performance, maintenance, etc.
    
    # Scheduling
    frequency = Column(SQLEnum(ScheduleFrequency), nullable=False)
    cron_expression = Column(String(100), nullable=True)  # For custom schedules
    time_of_day = Column(String(10), nullable=True)  # HH:MM format
    day_of_week = Column(Integer, nullable=True)  # 0-6 for weekly reports
    day_of_month = Column(Integer, nullable=True)  # 1-31 for monthly reports
    timezone = Column(String(50), nullable=False, default='UTC')
    
    # Report configuration
    filters = Column(JSON, nullable=True)  # JSON object with filter criteria
    columns = Column(JSON, nullable=True)  # JSON array of columns to include
    sort_by = Column(String(100), nullable=True)
    sort_order = Column(String(10), nullable=True, default='ASC')
    
    # Output settings
    export_format = Column(String(20), nullable=False, default='PDF')
    include_charts = Column(Boolean, default=True)
    include_summary = Column(Boolean, default=True)
    max_records = Column(Integer, nullable=True)  # Limit number of records
    
    # Delivery settings
    delivery_method = Column(SQLEnum(DeliveryMethod), nullable=False, default=DeliveryMethod.EMAIL)
    recipients = Column(JSON, nullable=True)  # JSON array of email addresses
    email_subject = Column(String(255), nullable=True)
    email_body = Column(Text, nullable=True)
    
    # Status and control
    status = Column(SQLEnum(ReportStatus), nullable=False, default=ReportStatus.ACTIVE)
    is_enabled = Column(Boolean, default=True)
    
    # Execution tracking
    last_run_at = Column(DateTime(timezone=True), nullable=True)
    next_run_at = Column(DateTime(timezone=True), nullable=True)
    last_run_status = Column(String(50), nullable=True)
    last_run_error = Column(Text, nullable=True)
    run_count = Column(Integer, default=0)
    success_count = Column(Integer, default=0)
    failure_count = Column(Integer, default=0)
    
    # File management
    retain_files_days = Column(Integer, default=30)  # How long to keep generated files
    file_name_template = Column(String(255), nullable=True)  # Template for file names
    
    # User management
    created_by_user_id = Column(String(36), ForeignKey("users.id"), nullable=False)
    created_by_name = Column(String(255), nullable=False)
    updated_by_user_id = Column(String(36), ForeignKey("users.id"), nullable=True)
    updated_by_name = Column(String(255), nullable=True)
    
    # Audit fields
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
    
    # Relationships
    created_by = relationship("User", foreign_keys=[created_by_user_id])
    updated_by = relationship("User", foreign_keys=[updated_by_user_id])
    executions = relationship("ScheduledReportExecution", back_populates="scheduled_report", cascade="all, delete-orphan")

class ScheduledReportExecution(Base):
    """
    Individual execution records for scheduled reports
    """
    __tablename__ = "scheduled_report_executions"

    id = Column(Integer, primary_key=True, autoincrement=True)
    
    # Report reference
    scheduled_report_id = Column(Integer, ForeignKey("scheduled_reports.id"), nullable=False)
    
    # Execution details
    execution_start = Column(DateTime(timezone=True), nullable=False)
    execution_end = Column(DateTime(timezone=True), nullable=True)
    duration_seconds = Column(Integer, nullable=True)
    
    # Status and results
    status = Column(String(50), nullable=False)  # RUNNING, SUCCESS, FAILED, CANCELLED
    records_processed = Column(Integer, nullable=True)
    file_size_bytes = Column(Integer, nullable=True)
    file_path = Column(String(500), nullable=True)
    
    # Error handling
    error_message = Column(Text, nullable=True)
    error_details = Column(Text, nullable=True)  # JSON with detailed error info
    retry_count = Column(Integer, default=0)
    
    # Delivery tracking
    delivery_status = Column(String(50), nullable=True)  # PENDING, SENT, FAILED
    delivery_attempts = Column(Integer, default=0)
    delivered_at = Column(DateTime(timezone=True), nullable=True)
    delivery_error = Column(Text, nullable=True)
    
    # Performance metrics
    query_time_ms = Column(Integer, nullable=True)
    generation_time_ms = Column(Integer, nullable=True)
    delivery_time_ms = Column(Integer, nullable=True)
    
    # Audit
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # Relationships
    scheduled_report = relationship("ScheduledReport", back_populates="executions")

class CustomReport(Base):
    """
    Custom report templates and saved configurations
    """
    __tablename__ = "custom_reports"

    id = Column(Integer, primary_key=True, autoincrement=True)
    
    # Report identification
    name = Column(String(255), nullable=False)
    description = Column(Text, nullable=True)
    category = Column(String(100), nullable=True)  # Asset, User, Financial, etc.
    
    # Report configuration
    data_source = Column(String(100), nullable=False)  # Primary table/view
    filters = Column(JSON, nullable=True)  # Filter criteria
    columns = Column(JSON, nullable=False)  # Columns to include
    joins = Column(JSON, nullable=True)  # Table joins configuration
    aggregations = Column(JSON, nullable=True)  # Grouping and aggregation rules
    sorting = Column(JSON, nullable=True)  # Sort configuration
    
    # Visualization settings
    chart_type = Column(String(50), nullable=True)  # bar, pie, line, table, etc.
    chart_config = Column(JSON, nullable=True)  # Chart-specific configuration
    
    # Access control
    is_public = Column(Boolean, default=False)
    allowed_users = Column(JSON, nullable=True)  # JSON array of user IDs
    allowed_roles = Column(JSON, nullable=True)  # JSON array of roles
    
    # Usage tracking
    usage_count = Column(Integer, default=0)
    last_used_at = Column(DateTime(timezone=True), nullable=True)
    
    # User management
    created_by_user_id = Column(String(36), ForeignKey("users.id"), nullable=False)
    created_by_name = Column(String(255), nullable=False)
    updated_by_user_id = Column(String(36), ForeignKey("users.id"), nullable=True)
    updated_by_name = Column(String(255), nullable=True)
    
    # Audit fields
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
    
    # Relationships
    created_by = relationship("User", foreign_keys=[created_by_user_id])
    updated_by = relationship("User", foreign_keys=[updated_by_user_id])

class ReportTemplate(Base):
    """
    Predefined report templates for common use cases
    """
    __tablename__ = "report_templates"

    id = Column(Integer, primary_key=True, autoincrement=True)
    
    # Template identification
    name = Column(String(255), nullable=False)
    description = Column(Text, nullable=True)
    category = Column(String(100), nullable=False)
    tags = Column(JSON, nullable=True)  # JSON array of tags for searching
    
    # Template configuration
    template_config = Column(JSON, nullable=False)  # Complete report configuration
    default_filters = Column(JSON, nullable=True)  # Default filter values
    required_parameters = Column(JSON, nullable=True)  # Parameters user must provide
    
    # Metadata
    is_system_template = Column(Boolean, default=False)  # System vs user-created
    is_active = Column(Boolean, default=True)
    version = Column(String(20), nullable=False, default='1.0')
    
    # Usage tracking
    usage_count = Column(Integer, default=0)
    rating = Column(Integer, nullable=True)  # 1-5 star rating
    
    # User management
    created_by_user_id = Column(String(36), ForeignKey("users.id"), nullable=True)
    created_by_name = Column(String(255), nullable=True)
    
    # Audit fields
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
    
    # Relationships
    created_by = relationship("User", foreign_keys=[created_by_user_id])
