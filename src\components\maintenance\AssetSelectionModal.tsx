import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, Dialog<PERSON>ontent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Search, ChevronLeft, ChevronRight, Loader2, AlertCircle } from 'lucide-react';
import { Asset, AvailableAssetsResponse } from '@/services/maintenanceService';
import { maintenanceService } from '@/services/maintenanceService';
import { useToast } from '@/components/ui/use-toast';

interface AssetSelectionModalProps {
  isOpen: boolean;
  onClose: () => void;
  onAssetSelected: (asset: Asset) => void;
}

export const AssetSelectionModal: React.FC<AssetSelectionModalProps> = ({
  isOpen,
  onClose,
  onAssetSelected
}) => {
  const { toast } = useToast();
  const [assets, setAssets] = useState<Asset[]>([]);
  const [loading, setLoading] = useState(false);
  const [selectedAsset, setSelectedAsset] = useState<Asset | null>(null);
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 10,
    total: 0,
    pages: 0
  });

  const [filters, setFilters] = useState({
    search: '',
    type: '',
    location: '',
    county: ''
  });

  useEffect(() => {
    if (isOpen) {
      fetchAvailableAssets();
    }
  }, [isOpen, pagination.page, filters]);

  const fetchAvailableAssets = async () => {
    try {
      setLoading(true);
      const response: AvailableAssetsResponse = await maintenanceService.getAvailableAssets({
        page: pagination.page,
        limit: pagination.limit,
        ...filters
      });

      setAssets(response.assets);
      setPagination(response.pagination);
    } catch (error) {
      console.error('Error fetching available assets:', error);
      toast({
        title: "Error",
        description: "Failed to load available assets. Please try again.",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  const handleFilterChange = (key: string, value: string) => {
    setFilters(prev => ({ ...prev, [key]: value }));
    setPagination(prev => ({ ...prev, page: 1 })); // Reset to first page
  };

  const handlePageChange = (newPage: number) => {
    setPagination(prev => ({ ...prev, page: newPage }));
  };

  const handleAssetSelect = (asset: Asset) => {
    setSelectedAsset(asset);
  };

  const handleConfirmSelection = () => {
    if (selectedAsset) {
      onAssetSelected(selectedAsset);
      handleClose();
    }
  };

  const handleClose = () => {
    setSelectedAsset(null);
    setFilters({ search: '', type: '', location: '', county: '' });
    setPagination(prev => ({ ...prev, page: 1 }));
    onClose();
  };

  const getStatusBadgeColor = (status: string) => {
    switch (status) {
      case 'Ready':
        return 'bg-green-100 text-green-800';
      case 'Failed':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getConditionBadgeColor = (condition: string) => {
    switch (condition) {
      case 'Excellent':
        return 'bg-green-100 text-green-800';
      case 'Good':
        return 'bg-blue-100 text-blue-800';
      case 'Fair':
        return 'bg-yellow-100 text-yellow-800';
      case 'Poor':
        return 'bg-orange-100 text-orange-800';
      case 'Damaged':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-6xl max-h-[90vh] overflow-hidden flex flex-col">
        <DialogHeader>
          <DialogTitle className="flex items-center space-x-2">
            <span>Select Asset for Maintenance</span>
            <Badge variant="outline" className="ml-2">
              {pagination.total} Available Assets
            </Badge>
          </DialogTitle>
        </DialogHeader>

        <div className="flex-1 flex flex-col space-y-4 overflow-hidden">
          {/* Filters */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Filter Assets</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div>
                  <Label htmlFor="search">Search</Label>
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                    <Input
                      id="search"
                      placeholder="Asset ID, model, serial..."
                      value={filters.search}
                      onChange={(e) => handleFilterChange('search', e.target.value)}
                      className="pl-10"
                    />
                  </div>
                </div>

                <div>
                  <Label htmlFor="type">Asset Type</Label>
                  <Select value={filters.type} onValueChange={(value) => handleFilterChange('type', value)}>
                    <SelectTrigger>
                      <SelectValue placeholder="All types" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="">All types</SelectItem>
                      <SelectItem value="AV computers">AV Computers</SelectItem>
                      <SelectItem value="AV printers">AV Printers</SelectItem>
                      <SelectItem value="tabs">Tabs</SelectItem>
                      <SelectItem value="scanners">Scanners</SelectItem>
                      <SelectItem value="pollpads">Pollpads</SelectItem>
                      <SelectItem value="monitors">Monitors</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label htmlFor="location">Location</Label>
                  <Input
                    id="location"
                    placeholder="Location"
                    value={filters.location}
                    onChange={(e) => handleFilterChange('location', e.target.value)}
                  />
                </div>

                <div>
                  <Label htmlFor="county">County</Label>
                  <Input
                    id="county"
                    placeholder="County"
                    value={filters.county}
                    onChange={(e) => handleFilterChange('county', e.target.value)}
                  />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Assets Table */}
          <Card className="flex-1 overflow-hidden">
            <CardHeader>
              <CardTitle className="text-lg">Available Assets</CardTitle>
              {selectedAsset && (
                <div className="bg-blue-50 p-3 rounded-lg">
                  <p className="text-sm text-blue-800">
                    Selected: <strong>{selectedAsset.asset_id}</strong> - {selectedAsset.type} {selectedAsset.model}
                  </p>
                </div>
              )}
            </CardHeader>
            <CardContent className="flex-1 overflow-hidden">
              {loading ? (
                <div className="flex items-center justify-center h-64">
                  <Loader2 className="h-8 w-8 animate-spin text-gray-500" />
                  <span className="ml-2 text-gray-500">Loading available assets...</span>
                </div>
              ) : assets.length === 0 ? (
                <div className="flex flex-col items-center justify-center h-64 text-gray-500">
                  <AlertCircle className="h-12 w-12 mb-4" />
                  <p className="text-lg font-medium">No assets available for maintenance</p>
                  <p className="text-sm">All assets may be in use or no assets match your filters</p>
                </div>
              ) : (
                <div className="overflow-auto h-full">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Select</TableHead>
                        <TableHead>Asset ID</TableHead>
                        <TableHead>Type</TableHead>
                        <TableHead>Model</TableHead>
                        <TableHead>Serial Number</TableHead>
                        <TableHead>Status</TableHead>
                        <TableHead>Condition</TableHead>
                        <TableHead>Location</TableHead>
                        <TableHead>County</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {assets.map((asset) => (
                        <TableRow
                          key={asset.id}
                          className={`cursor-pointer hover:bg-gray-50 ${
                            selectedAsset?.id === asset.id ? 'bg-blue-50 border-blue-200' : ''
                          }`}
                          onClick={() => handleAssetSelect(asset)}
                        >
                          <TableCell>
                            <input
                              type="radio"
                              name="selectedAsset"
                              checked={selectedAsset?.id === asset.id}
                              onChange={() => handleAssetSelect(asset)}
                              className="h-4 w-4 text-blue-600"
                            />
                          </TableCell>
                          <TableCell className="font-medium">{asset.asset_id}</TableCell>
                          <TableCell>{asset.type}</TableCell>
                          <TableCell>{asset.model || 'N/A'}</TableCell>
                          <TableCell>{asset.serial_number || 'N/A'}</TableCell>
                          <TableCell>
                            <Badge className={getStatusBadgeColor(asset.status)}>
                              {asset.status}
                            </Badge>
                          </TableCell>
                          <TableCell>
                            <Badge className={getConditionBadgeColor(asset.condition)}>
                              {asset.condition}
                            </Badge>
                          </TableCell>
                          <TableCell>{asset.location}</TableCell>
                          <TableCell>{asset.county || 'N/A'}</TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Pagination */}
          {pagination.pages > 1 && (
            <div className="flex items-center justify-between">
              <p className="text-sm text-gray-500">
                Showing {((pagination.page - 1) * pagination.limit) + 1} to{' '}
                {Math.min(pagination.page * pagination.limit, pagination.total)} of {pagination.total} results
              </p>
              <div className="flex items-center space-x-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handlePageChange(pagination.page - 1)}
                  disabled={pagination.page <= 1}
                >
                  <ChevronLeft className="h-4 w-4" />
                  Previous
                </Button>
                <span className="text-sm">
                  Page {pagination.page} of {pagination.pages}
                </span>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handlePageChange(pagination.page + 1)}
                  disabled={pagination.page >= pagination.pages}
                >
                  Next
                  <ChevronRight className="h-4 w-4" />
                </Button>
              </div>
            </div>
          )}
        </div>

        {/* Footer Actions */}
        <div className="flex justify-end space-x-3 pt-4 border-t">
          <Button variant="outline" onClick={handleClose}>
            Cancel
          </Button>
          <Button
            onClick={handleConfirmSelection}
            disabled={!selectedAsset}
            className="bg-blue-600 hover:bg-blue-700"
          >
            Select Asset
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
}; 