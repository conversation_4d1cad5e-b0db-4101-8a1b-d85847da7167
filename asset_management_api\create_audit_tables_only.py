"""
Database Migration Script for Audit Logging Tables Only
Run this script to create only the audit logging tables.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from sqlalchemy import create_engine, text, MetaData, Table
from app.config.database import DATABASE_URL
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def create_audit_tables_sql():
    """Create audit tables using raw SQL to avoid foreign key issues."""
    try:
        engine = create_engine(DATABASE_URL)
        
        # SQL statements to create audit tables
        audit_tables_sql = [
            """
            CREATE TABLE IF NOT EXISTS user_activity_logs (
                id INT AUTO_INCREMENT PRIMARY KEY,
                user_id VARCHAR(36) NOT NULL,
                user_name VARCHAR(255) NOT NULL,
                user_email VARCHAR(255) NOT NULL,
                activity_type ENUM('login', 'logout', 'create', 'update', 'delete', 'view', 'export', 'import', 'checkout', 'checkin', 'transfer', 'scan', 'maintenance', 'report_generate', 'settings_change', 'password_reset', 'permission_change') NOT NULL,
                action VARCHAR(255) NOT NULL,
                module VARCHAR(100) NOT NULL,
                resource_type VARCHAR(100),
                resource_id VARCHAR(100),
                resource_name VARCHAR(255),
                ip_address VARCHAR(45),
                user_agent TEXT,
                session_id VARCHAR(255),
                request_method VARCHAR(10),
                request_url TEXT,
                status ENUM('success', 'failed', 'warning', 'error') NOT NULL DEFAULT 'success',
                details TEXT,
                error_message TEXT,
                old_values TEXT,
                new_values TEXT,
                duration_ms INT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                INDEX idx_user_id (user_id),
                INDEX idx_activity_type (activity_type),
                INDEX idx_created_at (created_at),
                INDEX idx_module (module)
            )
            """,
            
            """
            CREATE TABLE IF NOT EXISTS system_access_logs (
                id INT AUTO_INCREMENT PRIMARY KEY,
                user_id VARCHAR(36),
                user_name VARCHAR(255),
                user_email VARCHAR(255),
                attempted_email VARCHAR(255),
                access_type ENUM('Login', 'Logout', 'Failed Login', 'Password Reset', 'Permission Change', 'Session Timeout') NOT NULL,
                ip_address VARCHAR(45) NOT NULL,
                user_agent TEXT,
                session_id VARCHAR(255),
                country VARCHAR(100),
                region VARCHAR(100),
                city VARCHAR(100),
                isp VARCHAR(255),
                status ENUM('success', 'failed', 'warning', 'error') NOT NULL,
                details TEXT,
                failure_reason VARCHAR(255),
                is_suspicious BOOLEAN DEFAULT FALSE,
                risk_score INT DEFAULT 0,
                login_time TIMESTAMP NULL,
                logout_time TIMESTAMP NULL,
                session_duration_minutes INT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                INDEX idx_user_id (user_id),
                INDEX idx_access_type (access_type),
                INDEX idx_ip_address (ip_address),
                INDEX idx_created_at (created_at),
                INDEX idx_status (status)
            )
            """,
            
            """
            CREATE TABLE IF NOT EXISTS asset_scan_logs (
                id INT AUTO_INCREMENT PRIMARY KEY,
                asset_id INT,
                asset_tag VARCHAR(100) NOT NULL,
                asset_name VARCHAR(255),
                asset_type VARCHAR(100),
                scan_type ENUM('Check-in', 'Check-out', 'Inventory', 'Maintenance', 'Audit') NOT NULL,
                scanner_device_id VARCHAR(100),
                scan_method VARCHAR(50),
                location_id VARCHAR(100),
                location_name VARCHAR(255),
                gps_coordinates VARCHAR(100),
                scanned_by_user_id VARCHAR(36) NOT NULL,
                scanned_by_name VARCHAR(255) NOT NULL,
                status ENUM('success', 'failed', 'warning', 'error') NOT NULL,
                is_valid_asset BOOLEAN DEFAULT TRUE,
                validation_errors TEXT,
                workflow_session_id VARCHAR(100),
                batch_id VARCHAR(100),
                notes TEXT,
                scan_duration_ms INT,
                device_info TEXT,
                scanned_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                INDEX idx_asset_id (asset_id),
                INDEX idx_asset_tag (asset_tag),
                INDEX idx_scan_type (scan_type),
                INDEX idx_scanned_by (scanned_by_user_id),
                INDEX idx_created_at (created_at)
            )
            """,
            
            """
            CREATE TABLE IF NOT EXISTS change_management_logs (
                id INT AUTO_INCREMENT PRIMARY KEY,
                change_type VARCHAR(100) NOT NULL,
                entity_type VARCHAR(100) NOT NULL,
                entity_id VARCHAR(100),
                entity_name VARCHAR(255),
                changed_by_user_id VARCHAR(36) NOT NULL,
                changed_by_name VARCHAR(255) NOT NULL,
                field_name VARCHAR(100),
                old_value TEXT,
                new_value TEXT,
                change_summary TEXT NOT NULL,
                reason TEXT,
                approval_required BOOLEAN DEFAULT FALSE,
                approved_by_user_id VARCHAR(36),
                approved_at TIMESTAMP NULL,
                ip_address VARCHAR(45),
                user_agent TEXT,
                session_id VARCHAR(255),
                status ENUM('success', 'failed', 'warning', 'error') NOT NULL DEFAULT 'success',
                rollback_available BOOLEAN DEFAULT FALSE,
                rollback_data TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                INDEX idx_entity_type (entity_type),
                INDEX idx_changed_by (changed_by_user_id),
                INDEX idx_created_at (created_at)
            )
            """,
            
            """
            CREATE TABLE IF NOT EXISTS report_generation_logs (
                id INT AUTO_INCREMENT PRIMARY KEY,
                report_name VARCHAR(255) NOT NULL,
                report_type VARCHAR(100) NOT NULL,
                report_category VARCHAR(100),
                generated_by_user_id VARCHAR(36) NOT NULL,
                generated_by_name VARCHAR(255) NOT NULL,
                filters_applied TEXT,
                date_range_start TIMESTAMP NULL,
                date_range_end TIMESTAMP NULL,
                export_format VARCHAR(20) NOT NULL,
                status ENUM('success', 'failed', 'warning', 'error') NOT NULL,
                records_count INT,
                file_size_bytes INT,
                file_path VARCHAR(500),
                generation_time_ms INT,
                query_time_ms INT,
                is_scheduled BOOLEAN DEFAULT FALSE,
                schedule_id VARCHAR(100),
                shared_with TEXT,
                download_count INT DEFAULT 0,
                ip_address VARCHAR(45),
                user_agent TEXT,
                generated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                expires_at TIMESTAMP NULL,
                INDEX idx_generated_by (generated_by_user_id),
                INDEX idx_report_type (report_type),
                INDEX idx_generated_at (generated_at)
            )
            """,
            
            """
            CREATE TABLE IF NOT EXISTS scheduled_reports (
                id INT AUTO_INCREMENT PRIMARY KEY,
                name VARCHAR(255) NOT NULL,
                description TEXT,
                report_type VARCHAR(100) NOT NULL,
                frequency ENUM('daily', 'weekly', 'monthly', 'quarterly', 'yearly', 'custom') NOT NULL,
                cron_expression VARCHAR(100),
                time_of_day VARCHAR(10),
                day_of_week INT,
                day_of_month INT,
                timezone VARCHAR(50) NOT NULL DEFAULT 'UTC',
                filters JSON,
                columns JSON,
                sort_by VARCHAR(100),
                sort_order VARCHAR(10) DEFAULT 'ASC',
                export_format VARCHAR(20) NOT NULL DEFAULT 'PDF',
                include_charts BOOLEAN DEFAULT TRUE,
                include_summary BOOLEAN DEFAULT TRUE,
                max_records INT,
                delivery_method ENUM('email', 'download', 'api', 'ftp') NOT NULL DEFAULT 'email',
                recipients JSON,
                email_subject VARCHAR(255),
                email_body TEXT,
                status ENUM('active', 'paused', 'disabled', 'error') NOT NULL DEFAULT 'active',
                is_enabled BOOLEAN DEFAULT TRUE,
                last_run_at TIMESTAMP NULL,
                next_run_at TIMESTAMP NULL,
                last_run_status VARCHAR(50),
                last_run_error TEXT,
                run_count INT DEFAULT 0,
                success_count INT DEFAULT 0,
                failure_count INT DEFAULT 0,
                retain_files_days INT DEFAULT 30,
                file_name_template VARCHAR(255),
                created_by_user_id VARCHAR(36) NOT NULL,
                created_by_name VARCHAR(255) NOT NULL,
                updated_by_user_id VARCHAR(36),
                updated_by_name VARCHAR(255),
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                INDEX idx_status (status),
                INDEX idx_next_run (next_run_at),
                INDEX idx_created_by (created_by_user_id)
            )
            """,
            
            """
            CREATE TABLE IF NOT EXISTS custom_reports (
                id INT AUTO_INCREMENT PRIMARY KEY,
                name VARCHAR(255) NOT NULL,
                description TEXT,
                category VARCHAR(100),
                data_source VARCHAR(100) NOT NULL,
                filters JSON,
                columns JSON NOT NULL,
                joins JSON,
                aggregations JSON,
                sorting JSON,
                chart_type VARCHAR(50),
                chart_config JSON,
                is_public BOOLEAN DEFAULT FALSE,
                allowed_users JSON,
                allowed_roles JSON,
                usage_count INT DEFAULT 0,
                last_used_at TIMESTAMP NULL,
                created_by_user_id VARCHAR(36) NOT NULL,
                created_by_name VARCHAR(255) NOT NULL,
                updated_by_user_id VARCHAR(36),
                updated_by_name VARCHAR(255),
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                INDEX idx_category (category),
                INDEX idx_created_by (created_by_user_id)
            )
            """
        ]
        
        with engine.connect() as conn:
            # Check existing tables
            existing_tables = conn.execute(text("SHOW TABLES")).fetchall()
            existing_table_names = [table[0] for table in existing_tables]
            logger.info(f"Existing tables: {len(existing_table_names)} tables found")
            
            # Create each table
            for i, sql in enumerate(audit_tables_sql):
                try:
                    conn.execute(text(sql))
                    conn.commit()
                    logger.info(f"Successfully created table {i+1}/{len(audit_tables_sql)}")
                except Exception as e:
                    logger.warning(f"Table {i+1} creation failed or already exists: {e}")
            
            # Verify new tables
            new_tables = conn.execute(text("SHOW TABLES")).fetchall()
            new_table_names = [table[0] for table in new_tables]
            
            audit_table_names = [
                'user_activity_logs', 'system_access_logs', 'asset_scan_logs',
                'change_management_logs', 'report_generation_logs', 'scheduled_reports',
                'custom_reports'
            ]
            
            created_audit_tables = [name for name in audit_table_names if name in new_table_names]
            logger.info(f"Audit tables now available: {created_audit_tables}")
                        
        logger.info("Audit tables creation completed successfully!")
        
    except Exception as e:
        logger.error(f"Error creating audit tables: {e}")
        raise

if __name__ == "__main__":
    logger.info("Starting audit tables creation...")
    
    try:
        create_audit_tables_sql()
        logger.info("All operations completed successfully!")
        
    except Exception as e:
        logger.error(f"Script failed: {e}")
        sys.exit(1)
