<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Frontend-Backend Connection Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-result { margin: 10px 0; padding: 10px; border-radius: 5px; }
        .success { background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .loading { background-color: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        button { padding: 10px 20px; margin: 5px; cursor: pointer; }
        pre { background-color: #f8f9fa; padding: 10px; border-radius: 5px; white-space: pre-wrap; }
    </style>
</head>
<body>
    <h1>Frontend-Backend Connection Test</h1>
    <p>This test will check if the frontend can communicate with the backend API.</p>
    
    <div>
        <button onclick="testHealthEndpoint()">Test Health Endpoint</button>
        <button onclick="testMaintenanceEndpoints()">Test Maintenance Endpoints</button>
        <button onclick="testAvailableAssets()">Test Available Assets</button>
        <button onclick="clearResults()">Clear Results</button>
    </div>
    
    <div id="results"></div>

    <script>
        const API_BASE_URL = 'http://localhost:8000';
        
        function addResult(title, content, type = 'success') {
            const resultsDiv = document.getElementById('results');
            const resultDiv = document.createElement('div');
            resultDiv.className = `test-result ${type}`;
            resultDiv.innerHTML = `
                <h3>${title}</h3>
                <pre>${content}</pre>
            `;
            resultsDiv.appendChild(resultDiv);
        }
        
        function clearResults() {
            document.getElementById('results').innerHTML = '';
        }
        
        async function testHealthEndpoint() {
            addResult('Testing Health Endpoint...', 'Sending request to /health', 'loading');
            
            try {
                const response = await fetch(`${API_BASE_URL}/health`);
                const data = await response.json();
                
                if (response.ok) {
                    addResult('✅ Health Endpoint Success', JSON.stringify(data, null, 2), 'success');
                } else {
                    addResult('❌ Health Endpoint Failed', `Status: ${response.status}\n${JSON.stringify(data, null, 2)}`, 'error');
                }
            } catch (error) {
                addResult('❌ Health Endpoint Error', `Network Error: ${error.message}`, 'error');
            }
        }
        
        async function testMaintenanceEndpoints() {
            addResult('Testing Maintenance Endpoints...', 'Checking available endpoints', 'loading');
            
            const endpoints = [
                '/api/maintenance/available-assets',
                '/api/maintenance',
                '/api/maintenance/stats'
            ];
            
            for (const endpoint of endpoints) {
                try {
                    const response = await fetch(`${API_BASE_URL}${endpoint}`);
                    const data = await response.text();
                    
                    if (response.ok) {
                        addResult(`✅ ${endpoint}`, `Status: ${response.status}\nResponse: ${data.substring(0, 200)}...`, 'success');
                    } else {
                        addResult(`❌ ${endpoint}`, `Status: ${response.status}\nError: ${data}`, 'error');
                    }
                } catch (error) {
                    addResult(`❌ ${endpoint}`, `Network Error: ${error.message}`, 'error');
                }
            }
        }
        
        async function testAvailableAssets() {
            addResult('Testing Available Assets...', 'Sending request to /api/maintenance/available-assets', 'loading');
            
            try {
                const response = await fetch(`${API_BASE_URL}/api/maintenance/available-assets`);
                const data = await response.json();
                
                if (response.ok) {
                    addResult('✅ Available Assets Success', JSON.stringify(data, null, 2), 'success');
                } else {
                    addResult('❌ Available Assets Failed', `Status: ${response.status}\n${JSON.stringify(data, null, 2)}`, 'error');
                }
            } catch (error) {
                addResult('❌ Available Assets Error', `Network Error: ${error.message}`, 'error');
            }
        }
        
        // Auto-run basic tests on page load
        window.onload = function() {
            addResult('🔍 Starting Connection Tests...', 'Testing frontend-backend connectivity', 'loading');
            setTimeout(testHealthEndpoint, 1000);
        };
    </script>
</body>
</html> 