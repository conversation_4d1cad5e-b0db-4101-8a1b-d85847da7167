import { AppLayout } from "@/components/layout/AppLayout";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { useState, useEffect } from "react";
import {
  Clock,
  Mail,
  Calendar,
  Settings,
  Plus,
  Edit,
  Trash2,
  FileText,
  Search,
  Bell,
  Users,
  List,
  Loader2,
  AlertCircle
} from "lucide-react";
import { ReportActions } from "@/components/reports/ReportActions";
import { Badge } from "@/components/ui/badge";
import { ScheduleCalendar } from "@/components/reports/ScheduleCalendar";

interface ScheduledReport {
  id: string;
  name: string;
  description: string;
  reportType: string;
  frequency: string;
  timeOfDay: string;
  exportFormat: string;
  deliveryMethod: string;
  recipients: string[];
  status: string;
  isEnabled: boolean;
  lastRunAt: string | null;
  nextRunAt: string | null;
  lastRunStatus: string | null;
  runCount: number;
  successCount: number;
  failureCount: number;
  createdBy: string;
  createdAt: string;
}

interface PaginationInfo {
  total: number;
  limit: number;
  offset: number;
  hasMore: boolean;
}

export default function ScheduledReportsPage() {
  const [isCreating, setIsCreating] = useState(false);
  const [activeView, setActiveView] = useState<'list' | 'calendar'>('list');
  const [scheduledReports, setScheduledReports] = useState<ScheduledReport[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [statusFilter, setStatusFilter] = useState('all');
  const [pagination, setPagination] = useState<PaginationInfo>({
    total: 0,
    limit: 50,
    offset: 0,
    hasMore: false
  });

  useEffect(() => {
    fetchScheduledReports();
  }, [statusFilter, pagination.offset]);

  const fetchScheduledReports = async () => {
    try {
      setLoading(true);
      setError(null);

      const params = new URLSearchParams({
        limit: pagination.limit.toString(),
        offset: pagination.offset.toString(),
      });

      if (statusFilter !== 'all') {
        params.append('status', statusFilter);
      }

      const response = await fetch(`http://localhost:8000/api/reports/scheduled?${params}`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error('Failed to fetch scheduled reports');
      }

      const result = await response.json();
      if (result.success) {
        setScheduledReports(result.data.scheduledReports);
        setPagination(result.data.pagination);
      } else {
        throw new Error('Invalid response format');
      }
    } catch (err) {
      console.error('Error fetching scheduled reports:', err);
      setError(err instanceof Error ? err.message : 'Failed to load scheduled reports');
    } finally {
      setLoading(false);
    }
  };

  const handleLoadMore = () => {
    setPagination(prev => ({ ...prev, offset: prev.offset + prev.limit }));
  };

  if (loading && scheduledReports.length === 0) {
    return (
      <AppLayout>
        <div className="flex items-center justify-center h-64">
          <Loader2 className="h-8 w-8 animate-spin" />
          <span className="ml-2">Loading scheduled reports...</span>
        </div>
      </AppLayout>
    );
  }

  if (error) {
    return (
      <AppLayout>
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <AlertCircle className="h-8 w-8 text-red-500 mx-auto mb-4" />
            <p className="text-red-600 mb-4">{error}</p>
            <Button onClick={fetchScheduledReports}>Retry</Button>
          </div>
        </div>
      </AppLayout>
    );
  }

  // No mock data - using only backend data

  return (
    <AppLayout>
      <div className="space-y-6 animate-fade-in p-6">
        <div className="flex flex-col md:flex-row md:items-center justify-between gap-4">
          <div>
            <h1 className="text-2xl font-bold text-zenith-blue-dark mb-2">Scheduled Reports</h1>
            <p className="text-muted-foreground">
              Automate report generation and delivery with customizable schedules
            </p>
          </div>
          <div className="flex gap-2">
            <Button
              variant={activeView === 'calendar' ? 'default' : 'outline'}
              className="flex items-center gap-2"
              onClick={() => setActiveView('calendar')}
            >
              <Calendar className="h-4 w-4" />
              Calendar View
            </Button>
            <Button
              variant={activeView === 'list' ? 'default' : 'outline'}
              className="flex items-center gap-2"
              onClick={() => setActiveView('list')}
            >
              <List className="h-4 w-4" />
              List View
            </Button>
          </div>
        </div>

        {/* Quick Stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Active Schedules</CardTitle>
              <Clock className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">3</div>
              <p className="text-xs text-muted-foreground">Running automatically</p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Next Report</CardTitle>
              <Bell className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">6:00 PM</div>
              <p className="text-xs text-muted-foreground">Daily Asset Status</p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Reports Sent</CardTitle>
              <Mail className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">47</div>
              <p className="text-xs text-muted-foreground">This month</p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Recipients</CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">12</div>
              <p className="text-xs text-muted-foreground">Unique recipients</p>
            </CardContent>
          </Card>
        </div>

        {activeView === 'calendar' ? (
          <ScheduleCalendar reports={scheduledReports} />
        ) : (
          <Tabs defaultValue="schedules" className="w-full">
            <TabsList>
              <TabsTrigger value="schedules">Active Schedules</TabsTrigger>
              <TabsTrigger value="history">Delivery History</TabsTrigger>
              <TabsTrigger value="archive">Report Archive</TabsTrigger>
            </TabsList>

            <TabsContent value="schedules" className="space-y-4">
              {loading ? (
                <div className="flex items-center justify-center py-8">
                  <Loader2 className="h-8 w-8 animate-spin" />
                  <span className="ml-2">Loading scheduled reports...</span>
                </div>
              ) : scheduledReports.length === 0 ? (
                <div className="text-center py-8">
                  <FileText className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                  <p className="text-muted-foreground">No scheduled reports found</p>
                  <Button onClick={() => setIsCreating(true)} className="mt-4">
                    <Plus className="h-4 w-4 mr-2" />
                    Create First Report
                  </Button>
                </div>
              ) : (
                scheduledReports.map((report) => (
                  <Card key={report.id}>
                    <CardHeader>
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-3">
                          <FileText className="h-5 w-5 text-blue-600" />
                          <div>
                            <CardTitle className="text-lg">{report.name}</CardTitle>
                            <CardDescription>{report.description}</CardDescription>
                          </div>
                        </div>
                        <div className="flex items-center gap-2">
                          <Badge variant={report.status === 'active' ? 'default' : 'secondary'}>
                            {report.status}
                          </Badge>
                          <Badge variant={report.isEnabled ? 'default' : 'outline'}>
                            {report.isEnabled ? 'Enabled' : 'Disabled'}
                          </Badge>
                          <ReportActions
                            onDownload={(format) => {
                              console.log(`Downloading ${report.name} in ${format} format`);
                            }}
                            onPrint={() => {
                              console.log(`Printing ${report.name}`);
                              window.print();
                            }}
                          />
                        </div>
                      </div>
                    </CardHeader>
                    <CardContent>
                      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                        <div>
                          <Label className="text-sm font-medium text-muted-foreground">Schedule</Label>
                          <p className="text-sm capitalize">{report.frequency} at {report.timeOfDay}</p>
                          <p className="text-xs text-muted-foreground">
                            Next: {report.nextRunAt ? new Date(report.nextRunAt).toLocaleString() : 'Not scheduled'}
                          </p>
                        </div>
                        <div>
                          <Label className="text-sm font-medium text-muted-foreground">Recipients</Label>
                          <div className="space-y-1">
                            {report.recipients.map((email, index) => (
                              <p key={index} className="text-sm">{email}</p>
                            ))}
                          </div>
                        </div>
                        <div>
                          <Label className="text-sm font-medium text-muted-foreground">Last Run</Label>
                          <p className="text-sm">
                            {report.lastRunAt ? new Date(report.lastRunAt).toLocaleString() : 'Never'}
                          </p>
                          <p className="text-xs text-muted-foreground">
                            Status: {report.lastRunStatus || 'N/A'}
                          </p>
                        </div>
                        <div>
                          <Label className="text-sm font-medium text-muted-foreground">Performance</Label>
                          <p className="text-sm">
                            {report.successCount}/{report.runCount} successful
                          </p>
                          <p className="text-xs text-muted-foreground">
                            Format: {report.exportFormat}
                          </p>
                        </div>
                      </div>
                  </CardContent>
                </Card>
              ))
              )}

              {pagination.hasMore && (
                <div className="flex justify-center mt-6">
                  <Button
                    onClick={handleLoadMore}
                    disabled={loading}
                    variant="outline"
                  >
                    {loading ? (
                      <>
                        <Loader2 className="h-4 w-4 animate-spin mr-2" />
                        Loading...
                      </>
                    ) : (
                      'Load More Reports'
                    )}
                  </Button>
                </div>
              )}
            </TabsContent>

            <TabsContent value="archive" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <FileText className="h-5 w-5" />
                    Historical Report Archive
                  </CardTitle>
                  <CardDescription>
                    Secure, searchable repository of past reports for audit and reference purposes
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  {/* Archive Search */}
                  <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
                    <div>
                      <Label>Search Reports</Label>
                      <Input placeholder="Report name or content" />
                    </div>
                    <div>
                      <Label>Date Range</Label>
                      <Select>
                        <SelectTrigger>
                          <SelectValue placeholder="Select range" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="week">Last Week</SelectItem>
                          <SelectItem value="month">Last Month</SelectItem>
                          <SelectItem value="quarter">Last Quarter</SelectItem>
                          <SelectItem value="year">Last Year</SelectItem>
                          <SelectItem value="custom">Custom Range</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <div>
                      <Label>Report Type</Label>
                      <Select>
                        <SelectTrigger>
                          <SelectValue placeholder="All types" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="all">All Types</SelectItem>
                          <SelectItem value="status">Status Reports</SelectItem>
                          <SelectItem value="performance">Performance Reports</SelectItem>
                          <SelectItem value="analytics">Analytics Reports</SelectItem>
                          <SelectItem value="maintenance">Maintenance Reports</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <div className="flex items-end">
                      <Button className="w-full">
                        <Search className="h-4 w-4 mr-2" />
                        Search Archive
                      </Button>
                    </div>
                  </div>

                  {/* Archived Reports */}
                  <div className="space-y-3">
                    {[
                      { name: "Daily Asset Status", date: "2024-01-14", type: "Status Report", size: "2.3 MB", format: "PDF" },
                      { name: "Weekly Performance Summary", date: "2024-01-12", type: "Performance Report", size: "1.8 MB", format: "Excel" },
                      { name: "Monthly Analytics Report", date: "2024-01-01", type: "Analytics Report", size: "4.1 MB", format: "PDF" },
                      { name: "Maintenance Schedule", date: "2024-01-08", type: "Maintenance Report", size: "1.2 MB", format: "CSV" },
                      { name: "Asset Utilization Q4", date: "2023-12-31", type: "Performance Report", size: "3.5 MB", format: "PDF" },
                    ].map((report, index) => (
                      <div key={index} className="flex items-center justify-between p-3 border rounded-lg hover:bg-gray-50">
                        <div className="flex items-center gap-3">
                          <FileText className="h-5 w-5 text-blue-600" />
                          <div>
                            <p className="font-medium">{report.name}</p>
                            <p className="text-sm text-muted-foreground">
                              {report.type} • {report.date} • {report.size}
                            </p>
                          </div>
                        </div>
                        <ReportActions 
                          onDownload={(format) => {
                            console.log(`Downloading ${report.name} in ${format} format`);
                          }}
                          onPrint={() => {
                            console.log(`Printing ${report.name}`);
                            window.print();
                          }}
                        />
                      </div>
                    ))}
                  </div>

                  {/* Archive Statistics */}
                  <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mt-6 pt-6 border-t">
                    <div className="text-center">
                      <p className="text-2xl font-bold">247</p>
                      <p className="text-sm text-muted-foreground">Total Reports</p>
                    </div>
                    <div className="text-center">
                      <p className="text-2xl font-bold">1.2 GB</p>
                      <p className="text-sm text-muted-foreground">Storage Used</p>
                    </div>
                    <div className="text-center">
                      <p className="text-2xl font-bold">18 months</p>
                      <p className="text-sm text-muted-foreground">Retention Period</p>
                    </div>
                    <div className="text-center">
                      <p className="text-2xl font-bold">99.9%</p>
                      <p className="text-sm text-muted-foreground">Availability</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="history" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>Delivery History</CardTitle>
                  <CardDescription>Recent report deliveries and their status</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    <div className="flex items-center justify-between p-3 bg-green-50 rounded">
                      <div className="flex items-center gap-3">
                        <Mail className="h-4 w-4 text-green-600" />
                        <div>
                          <p className="text-sm font-medium">Daily Asset Status</p>
                          <p className="text-xs text-muted-foreground">Delivered to 2 recipients • Yesterday 6:00 PM</p>
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        <Badge className="bg-green-100 text-green-800">Delivered</Badge>
                        <ReportActions 
                          onDownload={(format) => {
                            console.log(`Downloading Daily Asset Status in ${format} format`);
                          }}
                          onPrint={() => {
                            console.log(`Printing Daily Asset Status`);
                            window.print();
                          }}
                        />
                      </div>
                    </div>
                    
                    <div className="flex items-center justify-between p-3 bg-green-50 rounded">
                      <div className="flex items-center gap-3">
                        <Mail className="h-4 w-4 text-green-600" />
                        <div>
                          <p className="text-sm font-medium">Weekly Performance Summary</p>
                          <p className="text-xs text-muted-foreground">Delivered to 1 recipient • Last Friday 9:00 AM</p>
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        <Badge className="bg-green-100 text-green-800">Delivered</Badge>
                        <ReportActions 
                          onDownload={(format) => {
                            console.log(`Downloading Weekly Performance Summary in ${format} format`);
                          }}
                          onPrint={() => {
                            console.log(`Printing Weekly Performance Summary`);
                            window.print();
                          }}
                        />
                      </div>
                    </div>
                    
                    <div className="flex items-center justify-between p-3 bg-red-50 rounded">
                      <div className="flex items-center gap-3">
                        <Mail className="h-4 w-4 text-red-600" />
                        <div>
                          <p className="text-sm font-medium">Maintenance Alerts</p>
                          <p className="text-xs text-muted-foreground">Failed delivery • Last Monday 7:00 AM</p>
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        <Badge variant="destructive">Failed</Badge>
                        <Button size="sm" variant="outline">                       
                          Retry
                        </Button>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        )}
      </div>
    </AppLayout>
  );
}