"""
Database Migration Script for Audit Logging and Reports
Run this script to create the new tables for audit logging and reports functionality.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from sqlalchemy import create_engine, text
from app.config.database import DATABASE_URL, Base
from app.config.database import SessionLocal
import logging

# Import audit and reporting models to ensure they're registered
from app.models.audit_logs import User<PERSON>ctivityLog, SystemAccessLog, AssetScanLog, ChangeManagementLog, ReportGenerationLog
from app.models.scheduled_reports import ScheduledReport, ScheduledReportExecution, CustomReport, ReportTemplate

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def create_audit_tables():
    """Create audit logging and reports tables."""
    try:
        engine = create_engine(DATABASE_URL)
        
        # Create tables in order (respecting foreign key dependencies)
        tables_to_create = [
            # Audit tables
            'user_activity_logs',
            'system_access_logs', 
            'asset_scan_logs',
            'change_management_logs',
            'report_generation_logs',
            
            # Report tables
            'scheduled_reports',
            'scheduled_report_executions',
            'custom_reports',
            'report_templates'
        ]
        
        with engine.connect() as conn:
            # Check which tables already exist
            existing_tables = conn.execute(text("SHOW TABLES")).fetchall()
            existing_table_names = [table[0] for table in existing_tables]
            
            logger.info(f"Existing tables: {existing_table_names}")
            
            # Create all tables using SQLAlchemy metadata
            logger.info("Creating audit and report tables...")
            Base.metadata.create_all(engine, checkfirst=True)
            
            # Verify tables were created
            new_tables = conn.execute(text("SHOW TABLES")).fetchall()
            new_table_names = [table[0] for table in new_tables]
            
            created_tables = set(new_table_names) - set(existing_table_names)
            if created_tables:
                logger.info(f"Successfully created tables: {created_tables}")
            else:
                logger.info("All tables already existed or no new tables were created")
                
            # Show table structure for verification
            for table_name in tables_to_create:
                if table_name in new_table_names:
                    logger.info(f"\nTable structure for {table_name}:")
                    try:
                        columns = conn.execute(text(f"DESCRIBE {table_name}")).fetchall()
                        for column in columns:
                            logger.info(f"  {column[0]} - {column[1]} - {column[2]} - {column[3]}")
                    except Exception as e:
                        logger.warning(f"Could not describe table {table_name}: {e}")
                        
        logger.info("Audit tables creation completed successfully!")
        
    except Exception as e:
        logger.error(f"Error creating audit tables: {e}")
        raise

def insert_sample_data():
    """Insert some sample data for testing."""
    try:
        db = SessionLocal()
        
        # Insert sample report templates
        sample_templates = [
            {
                'name': 'Asset Performance Report',
                'description': 'Comprehensive asset utilization and performance metrics',
                'category': 'Asset',
                'template_config': {
                    'data_source': 'assets',
                    'columns': ['asset_id', 'type', 'status', 'condition', 'location'],
                    'filters': {'status': ['operational', 'maintenance']},
                    'chart_type': 'bar'
                },
                'is_system_template': True,
                'version': '1.0'
            },
            {
                'name': 'Maintenance Summary',
                'description': 'Detailed maintenance schedules and completion rates',
                'category': 'Maintenance',
                'template_config': {
                    'data_source': 'maintenance',
                    'columns': ['asset_id', 'maintenance_type', 'scheduled_date', 'completion_date', 'status'],
                    'chart_type': 'line'
                },
                'is_system_template': True,
                'version': '1.0'
            },
            {
                'name': 'User Activity Summary',
                'description': 'User activity and system usage patterns',
                'category': 'Audit',
                'template_config': {
                    'data_source': 'user_activity_logs',
                    'columns': ['user_name', 'activity_type', 'module', 'created_at'],
                    'chart_type': 'pie'
                },
                'is_system_template': True,
                'version': '1.0'
            }
        ]
        
        for template_data in sample_templates:
            # Check if template already exists
            existing = db.query(ReportTemplate).filter(
                ReportTemplate.name == template_data['name']
            ).first()
            
            if not existing:
                template = ReportTemplate(**template_data)
                db.add(template)
                logger.info(f"Added sample template: {template_data['name']}")
        
        db.commit()
        logger.info("Sample data insertion completed!")
        
    except Exception as e:
        logger.error(f"Error inserting sample data: {e}")
        db.rollback()
        raise
    finally:
        db.close()

if __name__ == "__main__":
    logger.info("Starting audit tables creation...")
    
    try:
        create_audit_tables()
        insert_sample_data()
        logger.info("All operations completed successfully!")
        
    except Exception as e:
        logger.error(f"Script failed: {e}")
        sys.exit(1)
