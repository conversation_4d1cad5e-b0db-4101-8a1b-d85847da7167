# app/models/audit_logs.py
from sqlalchemy import Column, Integer, String, Text, DateTime, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Enum as SQLEnum
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
from app.config.database import Base
import enum

class ActivityType(enum.Enum):
    LOGIN = "login"
    LOGOUT = "logout"
    CREATE = "create"
    UPDATE = "update"
    DELETE = "delete"
    VIEW = "view"
    EXPORT = "export"
    IMPORT = "import"
    CHECKOUT = "checkout"
    CHECKIN = "checkin"
    TRANSFER = "transfer"
    SCAN = "scan"
    MAINTENANCE = "maintenance"
    REPORT_GENERATE = "report_generate"
    SETTINGS_CHANGE = "settings_change"
    PASSWORD_RESET = "password_reset"
    PERMISSION_CHANGE = "permission_change"

class ActivityStatus(enum.Enum):
    SUCCESS = "success"
    FAILED = "failed"
    WARNING = "warning"
    ERROR = "error"

class AccessType(enum.Enum):
    LOGIN = "Login"
    LOGOUT = "Logout"
    FAILED_LOGIN = "Failed Login"
    PASSWORD_RESET = "Password Reset"
    PERMISSION_CHANGE = "Permission Change"
    SESSION_TIMEOUT = "Session Timeout"

class ScanType(enum.Enum):
    CHECKIN = "Check-in"
    CHECKOUT = "Check-out"
    INVENTORY = "Inventory"
    MAINTENANCE = "Maintenance"
    AUDIT = "Audit"

class UserActivityLog(Base):
    """
    Comprehensive user activity logging for audit trails
    """
    __tablename__ = "user_activity_logs"

    id = Column(Integer, primary_key=True, autoincrement=True)
    
    # User information
    user_id = Column(String(36), ForeignKey("users.id"), nullable=False)
    user_name = Column(String(255), nullable=False)  # Denormalized for performance
    user_email = Column(String(255), nullable=False)  # Denormalized for performance
    
    # Activity details
    activity_type = Column(SQLEnum(ActivityType), nullable=False)
    action = Column(String(255), nullable=False)  # Human readable action
    module = Column(String(100), nullable=False)  # System module (Authentication, Asset Management, etc.)
    resource_type = Column(String(100), nullable=True)  # Type of resource affected (Asset, User, Report, etc.)
    resource_id = Column(String(100), nullable=True)  # ID of the resource affected
    resource_name = Column(String(255), nullable=True)  # Name of the resource affected
    
    # Technical details
    ip_address = Column(String(45), nullable=True)  # IPv4/IPv6 support
    user_agent = Column(Text, nullable=True)
    session_id = Column(String(255), nullable=True)
    request_method = Column(String(10), nullable=True)  # GET, POST, PUT, DELETE
    request_url = Column(Text, nullable=True)
    
    # Status and results
    status = Column(SQLEnum(ActivityStatus), nullable=False, default=ActivityStatus.SUCCESS)
    details = Column(Text, nullable=True)  # Additional details about the activity
    error_message = Column(Text, nullable=True)  # Error details if status is FAILED/ERROR
    
    # Data changes (for audit purposes)
    old_values = Column(Text, nullable=True)  # JSON string of old values
    new_values = Column(Text, nullable=True)  # JSON string of new values
    
    # Timing
    duration_ms = Column(Integer, nullable=True)  # Request duration in milliseconds
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # Relationships
    user = relationship("User", back_populates="activity_logs")

class SystemAccessLog(Base):
    """
    System access and authentication logging
    """
    __tablename__ = "system_access_logs"

    id = Column(Integer, primary_key=True, autoincrement=True)
    
    # User information
    user_id = Column(String(36), ForeignKey("users.id"), nullable=True)  # Nullable for failed logins
    user_name = Column(String(255), nullable=True)
    user_email = Column(String(255), nullable=True)
    attempted_email = Column(String(255), nullable=True)  # For failed login attempts
    
    # Access details
    access_type = Column(SQLEnum(AccessType), nullable=False)
    ip_address = Column(String(45), nullable=False)
    user_agent = Column(Text, nullable=True)
    session_id = Column(String(255), nullable=True)
    
    # Geographic and network info
    country = Column(String(100), nullable=True)
    region = Column(String(100), nullable=True)
    city = Column(String(100), nullable=True)
    isp = Column(String(255), nullable=True)
    
    # Status and details
    status = Column(SQLEnum(ActivityStatus), nullable=False)
    details = Column(Text, nullable=True)
    failure_reason = Column(String(255), nullable=True)  # Specific reason for failures
    
    # Security flags
    is_suspicious = Column(Boolean, default=False)
    risk_score = Column(Integer, default=0)  # 0-100 risk assessment
    
    # Timing
    login_time = Column(DateTime(timezone=True), nullable=True)
    logout_time = Column(DateTime(timezone=True), nullable=True)
    session_duration_minutes = Column(Integer, nullable=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # Relationships
    user = relationship("User", back_populates="access_logs")

class AssetScanLog(Base):
    """
    Asset scanning and barcode verification logging
    """
    __tablename__ = "asset_scan_logs"

    id = Column(Integer, primary_key=True, autoincrement=True)

    # Asset information
    asset_id = Column(Integer, ForeignKey("assets.id"), nullable=True)  # Nullable for invalid scans
    asset_tag = Column(String(100), nullable=False)  # The scanned tag/barcode
    asset_name = Column(String(255), nullable=True)
    asset_type = Column(String(100), nullable=True)

    # Scan details
    scan_type = Column(SQLEnum(ScanType), nullable=False)
    scanner_device_id = Column(String(100), nullable=True)  # Device used for scanning
    scan_method = Column(String(50), nullable=True)  # QR, Barcode, NFC, Manual

    # Location information
    location_id = Column(String(100), nullable=True)
    location_name = Column(String(255), nullable=True)
    gps_coordinates = Column(String(100), nullable=True)  # lat,lng format

    # User information
    scanned_by_user_id = Column(String(36), ForeignKey("users.id"), nullable=False)
    scanned_by_name = Column(String(255), nullable=False)

    # Status and validation
    status = Column(SQLEnum(ActivityStatus), nullable=False)
    is_valid_asset = Column(Boolean, default=True)
    validation_errors = Column(Text, nullable=True)  # JSON array of validation errors

    # Context information
    workflow_session_id = Column(String(100), nullable=True)  # Related workflow session
    batch_id = Column(String(100), nullable=True)  # For bulk scanning operations
    notes = Column(Text, nullable=True)

    # Technical details
    scan_duration_ms = Column(Integer, nullable=True)
    device_info = Column(Text, nullable=True)  # JSON with device details

    # Timing
    scanned_at = Column(DateTime(timezone=True), server_default=func.now())
    created_at = Column(DateTime(timezone=True), server_default=func.now())

    # Relationships
    asset = relationship("Asset", back_populates="scan_logs")
    scanned_by = relationship("User", back_populates="scan_logs")

class ChangeManagementLog(Base):
    """
    System configuration and data change logging
    """
    __tablename__ = "change_management_logs"

    id = Column(Integer, primary_key=True, autoincrement=True)

    # Change details
    change_type = Column(String(100), nullable=False)  # CONFIG_CHANGE, DATA_CHANGE, SCHEMA_CHANGE
    entity_type = Column(String(100), nullable=False)  # Table/Model name
    entity_id = Column(String(100), nullable=True)  # Record ID if applicable
    entity_name = Column(String(255), nullable=True)  # Human readable name

    # User information
    changed_by_user_id = Column(String(36), ForeignKey("users.id"), nullable=False)
    changed_by_name = Column(String(255), nullable=False)

    # Change data
    field_name = Column(String(100), nullable=True)  # Specific field changed
    old_value = Column(Text, nullable=True)  # Previous value
    new_value = Column(Text, nullable=True)  # New value
    change_summary = Column(Text, nullable=False)  # Human readable summary

    # Context
    reason = Column(Text, nullable=True)  # Reason for change
    approval_required = Column(Boolean, default=False)
    approved_by_user_id = Column(String(36), ForeignKey("users.id"), nullable=True)
    approved_at = Column(DateTime(timezone=True), nullable=True)

    # Technical details
    ip_address = Column(String(45), nullable=True)
    user_agent = Column(Text, nullable=True)
    session_id = Column(String(255), nullable=True)

    # Status
    status = Column(SQLEnum(ActivityStatus), nullable=False, default=ActivityStatus.SUCCESS)
    rollback_available = Column(Boolean, default=False)
    rollback_data = Column(Text, nullable=True)  # JSON data for rollback

    # Timing
    created_at = Column(DateTime(timezone=True), server_default=func.now())

    # Relationships
    changed_by = relationship("User", foreign_keys=[changed_by_user_id], back_populates="change_logs")
    approved_by = relationship("User", foreign_keys=[approved_by_user_id])

class ReportGenerationLog(Base):
    """
    Report generation and export activity logging
    """
    __tablename__ = "report_generation_logs"

    id = Column(Integer, primary_key=True, autoincrement=True)

    # Report details
    report_name = Column(String(255), nullable=False)
    report_type = Column(String(100), nullable=False)  # CUSTOM, SCHEDULED, AUDIT, etc.
    report_category = Column(String(100), nullable=True)  # Asset, User, Financial, etc.

    # User information
    generated_by_user_id = Column(String(36), ForeignKey("users.id"), nullable=False)
    generated_by_name = Column(String(255), nullable=False)

    # Generation parameters
    filters_applied = Column(Text, nullable=True)  # JSON of filters used
    date_range_start = Column(DateTime(timezone=True), nullable=True)
    date_range_end = Column(DateTime(timezone=True), nullable=True)
    export_format = Column(String(20), nullable=False)  # PDF, Excel, CSV, etc.

    # Results
    status = Column(SQLEnum(ActivityStatus), nullable=False)
    records_count = Column(Integer, nullable=True)  # Number of records in report
    file_size_bytes = Column(Integer, nullable=True)
    file_path = Column(String(500), nullable=True)  # Path to generated file

    # Performance metrics
    generation_time_ms = Column(Integer, nullable=True)
    query_time_ms = Column(Integer, nullable=True)

    # Access and sharing
    is_scheduled = Column(Boolean, default=False)
    schedule_id = Column(String(100), nullable=True)
    shared_with = Column(Text, nullable=True)  # JSON array of recipients
    download_count = Column(Integer, default=0)

    # Technical details
    ip_address = Column(String(45), nullable=True)
    user_agent = Column(Text, nullable=True)

    # Timing
    generated_at = Column(DateTime(timezone=True), server_default=func.now())
    expires_at = Column(DateTime(timezone=True), nullable=True)  # When file expires

    # Relationships
    generated_by = relationship("User", back_populates="report_logs")
