# app/models/maintenance.py
from sqlalchemy import Column, String, DateTime, Enum, Text, DECIMAL
from sqlalchemy.sql import func
from app.config.database import Base
import enum
from datetime import datetime

class MaintenanceCategory(str, enum.Enum):
    AV_COMPUTERS = "AV computers"
    AV_PRINTERS = "AV printers"
    TABS = "tabs"
    OTHERS = "others"
    SCANNERS = "scanners"
    POLLPADS = "pollpads"
    MONITORS = "monitors"

class RequestType(str, enum.Enum):
    PREVENTIVE = "preventive"
    CORRECTIVE = "corrective"
    EMERGENCY = "emergency"

class Priority(str, enum.Enum):
    HIGH = "high"
    LOW = "low"
    MEDIUM = "medium"
    CRITICAL = "critical"

class MaintenanceStatus(str, enum.Enum):
    REQUESTED = "REQUESTED"
    SCHEDULED = "SCHEDULED"
    IN_PROGRESS = "IN_PROGRESS"
    TESTING = "TESTING"
    COMPLETED = "COMPLETED"

class ScheduledFrequency(str, enum.Enum):
    ONCE = "once"
    WEEKLY = "weekly"
    MONTHLY = "monthly"
    QUARTERLY = "quarterly"
    HALF_YEARLY = "half-yearly"
    YEARLY = "yearly"

class Maintenance(Base):
    __tablename__ = "maintenance"

    asset_tag = Column(String(255), primary_key=True)
    category = Column(String(50), nullable=False)  # Temporarily use String instead of Enum
    name = Column(String(255), nullable=False)
    request_type = Column(String(50), nullable=False)  # Temporarily use String instead of Enum
    priority = Column(String(50), nullable=False, default="medium")  # Temporarily use String instead of Enum
    assigned_technician = Column(String(255), nullable=True)
    description = Column(Text, nullable=True)
    scheduled_date = Column(DateTime, nullable=True)
    scheduled_frequency = Column(String(50), nullable=True)  # Temporarily use String instead of Enum
    estimated_hours = Column(DECIMAL(8, 2), nullable=True)
    estimated_cost = Column(DECIMAL(10, 2), nullable=True)
    notes = Column(Text, nullable=True)
    status = Column(String(50), nullable=False, default="REQUESTED")  # Temporarily use String instead of Enum
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())

    def is_overdue(self) -> bool:
        """Check if maintenance is overdue."""
        if not self.scheduled_date:
            return False
        return datetime.utcnow() > self.scheduled_date and self.status != MaintenanceStatus.COMPLETED

    def get_priority_weight(self) -> int:
        """Get priority weight for sorting."""
        weights = {
            Priority.CRITICAL: 4,
            Priority.HIGH: 3,
            Priority.MEDIUM: 2,
            Priority.LOW: 1
        }
        return weights.get(self.priority, 0)

    def get_days_until_scheduled(self) -> int:
        """Calculate days until scheduled date."""
        if not self.scheduled_date:
            return None
        today = datetime.utcnow()
        diff_time = self.scheduled_date - today
        return diff_time.days

    def to_dict(self) -> dict:
        """Convert maintenance object to dictionary."""
        return {
            "asset_tag": self.asset_tag,
            "category": str(self.category) if self.category else None,
            "name": self.name,
            "request_type": str(self.request_type) if self.request_type else None,
            "priority": str(self.priority) if self.priority else None,
            "assigned_technician": self.assigned_technician,
            "description": self.description,
            "scheduled_date": self.scheduled_date,
            "scheduled_frequency": str(self.scheduled_frequency) if self.scheduled_frequency else None,
            "estimated_hours": float(self.estimated_hours) if self.estimated_hours else None,
            "estimated_cost": float(self.estimated_cost) if self.estimated_cost else None,
            "notes": self.notes,
            "status": str(self.status) if self.status else None,
            "created_at": self.created_at,
            "updated_at": self.updated_at,
            "is_overdue": self.is_overdue(),
            "priority_weight": self.get_priority_weight(),
            "days_until_scheduled": self.get_days_until_scheduled()
        }