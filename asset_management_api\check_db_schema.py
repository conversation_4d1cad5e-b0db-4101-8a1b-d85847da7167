#!/usr/bin/env python3
"""
Check the actual database schema enum definitions.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from sqlalchemy import create_engine, text
from app.config.database import DATABASE_URL
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def check_db_schema():
    """Check the actual database schema enum definitions."""
    
    try:
        engine = create_engine(DATABASE_URL)
        
        with engine.connect() as connection:
            logger.info("🔍 Checking database schema for maintenance table...")
            
            # Get the table schema
            result = connection.execute(text("""
                SHOW CREATE TABLE maintenance
            """))
            
            create_table = result.fetchone()
            if create_table:
                logger.info("📋 Maintenance table schema:")
                print(create_table[1])
            
            # Also check column details
            logger.info("\n📊 Column details:")
            columns = connection.execute(text("""
                DESCRIBE maintenance
            """))
            
            for column in columns:
                logger.info(f"  {column.Field}: {column.Type}")
                
    except Exception as e:
        logger.error(f"❌ Error: {e}")

if __name__ == "__main__":
    check_db_schema() 