import React, { useState, useEffect } from 'react';
import { AppLayout } from "@/components/layout/AppLayout";
import { <PERSON>, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { DatePickerWithRange } from "@/components/ui/date-picker-with-range";
import { Download, MapPin, Loader2, AlertCircle } from 'lucide-react';
import { ReportActions } from "@/components/reports/ReportActions";

interface AssetMovement {
  id: string;
  assetId: string;
  assetName: string;
  fromLocation: string;
  toLocation: string;
  transferType: string;
  movedBy: string;
  reason: string;
  timestamp: string;
  status: 'completed' | 'in-transit' | 'pending' | 'cancelled';
  notes?: string;
}

interface PaginationInfo {
  total: number;
  limit: number;
  offset: number;
  hasMore: boolean;
}

// No mock data - using only backend data

export default function AssetMovementPage() {
  const [movements, setMovements] = useState<AssetMovement[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [dateRange, setDateRange] = useState<{ from: Date; to: Date } | undefined>();
  const [pagination, setPagination] = useState<PaginationInfo>({
    total: 0,
    limit: 50,
    offset: 0,
    hasMore: false
  });

  useEffect(() => {
    fetchMovements();
  }, [statusFilter, pagination.offset]);

  const fetchMovements = async () => {
    try {
      setLoading(true);
      setError(null);

      const params = new URLSearchParams({
        limit: pagination.limit.toString(),
        offset: pagination.offset.toString(),
      });

      if (statusFilter !== 'all') {
        params.append('status', statusFilter);
      }

      if (dateRange?.from) {
        params.append('start_date', dateRange.from.toISOString());
      }

      if (dateRange?.to) {
        params.append('end_date', dateRange.to.toISOString());
      }

      const response = await fetch(`http://localhost:8000/api/reports/audit/asset-movement?${params}`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error('Failed to fetch asset movements');
      }

      const result = await response.json();
      if (result.success) {
        setMovements(result.data.movements);
        setPagination(result.data.pagination);
      } else {
        throw new Error('Invalid response format');
      }
    } catch (err) {
      console.error('Error fetching asset movements:', err);
      setError(err instanceof Error ? err.message : 'Failed to load asset movements');
    } finally {
      setLoading(false);
    }
  };

  const handleLoadMore = () => {
    setPagination(prev => ({ ...prev, offset: prev.offset + prev.limit }));
  };

  // Filter movements locally for search term
  const filteredMovements = movements.filter(movement => {
    if (!searchTerm) return true;
    const searchLower = searchTerm.toLowerCase();
    return (
      movement.assetName.toLowerCase().includes(searchLower) ||
      movement.fromLocation.toLowerCase().includes(searchLower) ||
      movement.toLocation.toLowerCase().includes(searchLower) ||
      movement.movedBy.toLowerCase().includes(searchLower)
    );
  });

  if (loading && movements.length === 0) {
    return (
      <AppLayout>
        <div className="flex items-center justify-center h-64">
          <Loader2 className="h-8 w-8 animate-spin" />
          <span className="ml-2">Loading asset movements...</span>
        </div>
      </AppLayout>
    );
  }

  if (error) {
    return (
      <AppLayout>
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <AlertCircle className="h-8 w-8 text-red-500 mx-auto mb-4" />
            <p className="text-red-600 mb-4">{error}</p>
            <Button onClick={fetchMovements}>Retry</Button>
          </div>
        </div>
      </AppLayout>
    );
  }

  const handleExport = () => {
    const csvContent = [
      ['Asset ID', 'Asset Name', 'From Location', 'To Location', 'Moved By', 'Reason', 'Timestamp', 'Status'].join(','),
      ...filteredMovements.map(movement => [
        movement.assetId,
        movement.assetName,
        movement.fromLocation,
        movement.toLocation,
        movement.movedBy,
        movement.reason,
        new Date(movement.timestamp).toLocaleString(),
        movement.status
      ].join(','))
    ].join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `asset-movements-${new Date().toISOString().split('T')[0]}.csv`;
    a.click();
    window.URL.revokeObjectURL(url);
  };

  return (
    <AppLayout>
      <div className="container mx-auto py-6">
        <div className="mb-6">
          <h1 className="text-3xl font-bold">Asset Movement Tracking</h1>
          <p className="text-muted-foreground">Track and analyze asset location changes and movements</p>
        </div>

        <Card className="mb-6">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <MapPin className="h-5 w-5" />
              Movement Analysis
            </CardTitle>
            <CardDescription>
              Monitor asset movements between locations and their current status
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
              <Input
                placeholder="Search assets, locations, or users..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full"
              />
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger>
                  <SelectValue placeholder="Filter by status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Statuses</SelectItem>
                  <SelectItem value="Completed">Completed</SelectItem>
                  <SelectItem value="In Transit">In Transit</SelectItem>
                  <SelectItem value="Scheduled">Scheduled</SelectItem>
                </SelectContent>
              </Select>
              <DatePickerWithRange
                date={dateRange}
                setDate={setDateRange as any}
              />
              <Button onClick={handleExport} className="flex items-center gap-2">
                <Download className="h-4 w-4" />
                Export Data
              </Button>
            </div>

            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Asset</TableHead>
                    <TableHead>From</TableHead>
                    <TableHead>To</TableHead>
                    <TableHead>Moved By</TableHead>
                    <TableHead>Reason</TableHead>
                    <TableHead>Timestamp</TableHead>
                    <TableHead>Status</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredMovements.map((movement) => (
                    <TableRow key={movement.id}>
                      <TableCell>
                        <div className="flex flex-col">
                          <span className="font-medium">{movement.assetId}</span>
                          <span className="text-sm text-muted-foreground">{movement.assetName}</span>
                        </div>
                      </TableCell>
                      <TableCell>{movement.fromLocation}</TableCell>
                      <TableCell>{movement.toLocation}</TableCell>
                      <TableCell>{movement.movedBy}</TableCell>
                      <TableCell>{movement.reason}</TableCell>
                      <TableCell>{new Date(movement.timestamp).toLocaleString()}</TableCell>
                      <TableCell>
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                          movement.status === 'completed' ? 'bg-green-100 text-green-800' :
                          movement.status === 'in-transit' ? 'bg-blue-100 text-blue-800' :
                          movement.status === 'pending' ? 'bg-yellow-100 text-yellow-800' :
                          'bg-red-100 text-red-800'
                        }`}>
                          {movement.status}
                        </span>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>

            {pagination.hasMore && (
              <div className="flex justify-center mt-4">
                <Button
                  onClick={handleLoadMore}
                  disabled={loading}
                  variant="outline"
                >
                  {loading ? (
                    <>
                      <Loader2 className="h-4 w-4 animate-spin mr-2" />
                      Loading...
                    </>
                  ) : (
                    'Load More'
                  )}
                </Button>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </AppLayout>
  );
}
