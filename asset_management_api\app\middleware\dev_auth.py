#!/usr/bin/env python3
"""
Development authentication bypass for testing.
This allows endpoints to work without JWT tokens during development.
"""

from fastapi import Depends
from sqlalchemy.orm import Session
from app.config.database import get_db
from app.models.user import User
import os

def get_dev_user(db: Session = Depends(get_db)) -> User:
    """
    Development authentication bypass.
    Returns a test user without requiring JWT token.
    Only works when NODE_ENV=development.
    """
    # Only allow in development mode
    if os.getenv("NODE_ENV", "development") != "development":
        raise Exception("Development auth bypass only allowed in development mode")
    
    # Get or create a test user
    test_user = db.query(User).filter(User.email == "<EMAIL>").first()
    
    if not test_user:
        # Create a test user if it doesn't exist
        test_user = User(
            email="<EMAIL>",
            first_name="Test",
            last_name="User",
            role="admin",
            access_level="state",
            status=True,
            login_enabled=True,
            user_group="admin",
            login_id="test_user"
        )
        db.add(test_user)
        db.commit()
        db.refresh(test_user)
    
    return test_user

def get_dev_or_real_user(
    db: Session = Depends(get_db)
) -> User:
    """
    Get development user for testing.
    This bypasses authentication in development mode.
    """
    return get_dev_user(db)
