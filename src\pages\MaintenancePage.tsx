import { useState } from "react";
import { AppLayout } from "@/components/layout/AppLayout";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rigger, <PERSON><PERSON>Content } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { MaintenanceSimplified as MaintenanceTab } from "@/pages/maintenance/MaintenanceSimplified";
//import { MaintenanceDashboardTab } from "@/pages/maintenance/MaintenanceDashboardTab";
//import { MaintenanceAssetsTab } from "@/pages/maintenance/MaintenanceAssetsTab";
//import { MaintenanceNotificationsTab } from "@/pages/maintenance/MaintenanceNotificationsTab";
// import { RepairTab } from "@/pages/maintenance/RepairTab";
import { 
  LayoutDashboard, 
  Box, 
  Wrench, 
  Bell 
} from "lucide-react";
import { useAppContext } from "@/context/AppContext";
import { cn } from "@/lib/utils";

export default function MaintenancePage() {  
  // Set default tab to 'maintenance' so it loads automatically
  const [activeTab, setActiveTab] = useState("maintenance");
  const { state } = useAppContext();
  
  // Calculate badge counts
  const getMaintenanceCount = () => state.maintenanceRequests.filter(r => r.status !== 'COMPLETED').length;

  return (
    <AppLayout>
      <div className="max-w-7xl mx-auto space-y-8 p-8 bg-white rounded-lg shadow-md min-h-[80vh]">
        <div className="flex justify-between items-center mb-6">
          <div>
            <h1 className="text-4xl font-bold text-gray-900">Asset Maintenance Management</h1>
            <p className="text-gray-500 mt-2 text-lg">Election Equipment Tracking & Maintenance System</p>
          </div>
        </div>
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="w-full border-b bg-transparent p-0 justify-start space-x-8">
            {/* <TabsTrigger
              value="dashboard"
              className="flex items-center space-x-2 border-b-2 border-transparent data-[state=active]:border-blue-500 px-1 pb-4"
            >
              <LayoutDashboard className="h-4 w-4" />
              <span>Dashboard</span>
            </TabsTrigger> */}

            
            
            <TabsTrigger
              value="maintenance"
              className="flex items-center space-x-2 border-b-2 border-transparent data-[state=active]:border-blue-500 px-1 pb-4"
            >
              <Wrench className="h-4 w-4" />
              <span className="text-xl font-bold">Maintenance</span>
              {getMaintenanceCount() > 0 && (
                <Badge variant="secondary" className="ml-2 bg-blue-100 text-blue-800">
                  {getMaintenanceCount()}
                </Badge>
              )}
            </TabsTrigger>


            {/* <TabsTrigger
              value="repair"
              className="flex items-center space-x-2 border-b-2 border-transparent data-[state=active]:border-blue-500 px-1 pb-4"
            >
              <Wrench className="h-4 w-4" />
              <span>Repair</span>
            </TabsTrigger> */}
          </TabsList>

          {/* <TabsContent value="dashboard" className="mt-6">
            <MaintenanceDashboardTab />
          </TabsContent> */}

          <TabsContent value="maintenance" className="mt-6">
            <MaintenanceTab />
          </TabsContent>

          {/* <TabsContent value="repair" className="mt-6">
            <RepairTab />
          </TabsContent> */}
        </Tabs>
      </div>
    </AppLayout>
  );
}
