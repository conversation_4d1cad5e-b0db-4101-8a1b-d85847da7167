import React, { useState } from 'react';
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Calendar } from '@/components/ui/calendar';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { CalendarIcon, ArrowRight, CheckCircle, Loader2 } from 'lucide-react';
import { format } from 'date-fns';
import { cn } from '@/lib/utils';
import { 
  Asset, 
  MaintenanceWorkflowRequest, 
  MaintenanceWorkflowResponse, 
  maintenanceService 
} from '@/services/maintenanceService';
import { AssetSelectionModal } from './AssetSelectionModal';
import { useToast } from '@/components/ui/use-toast';

interface MaintenanceWorkflowModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess?: (result: MaintenanceWorkflowResponse) => void;
}

type WorkflowStep = 'select-asset' | 'create-maintenance' | 'confirmation';

export const MaintenanceWorkflowModal: React.FC<MaintenanceWorkflowModalProps> = ({
  isOpen,
  onClose,
  onSuccess
}) => {
  const { toast } = useToast();
  const [currentStep, setCurrentStep] = useState<WorkflowStep>('select-asset');
  const [selectedAsset, setSelectedAsset] = useState<Asset | null>(null);
  const [isAssetSelectionOpen, setIsAssetSelectionOpen] = useState(false);
  const [loading, setLoading] = useState(false);
  const [result, setResult] = useState<MaintenanceWorkflowResponse | null>(null);

  const [formData, setFormData] = useState<MaintenanceWorkflowRequest>({
    asset_tag: '',
    category: 'AV_COMPUTERS',
    name: '',
    request_type: 'preventive',
    priority: 'medium',
    description: '',
    scheduled_date: undefined,
    scheduled_frequency: 'once',
    estimated_hours: undefined,
    estimated_cost: undefined,
    notes: '',
    assigned_technician: ''
  });

  const handleAssetSelected = (asset: Asset) => {
    setSelectedAsset(asset);
    setFormData(prev => ({
      ...prev,
      asset_tag: asset.asset_id
    }));
    setCurrentStep('create-maintenance');
    setIsAssetSelectionOpen(false);
  };

  const handleSubmitMaintenance = async () => {
    try {
      setLoading(true);

      if (!selectedAsset) {
        toast({
          title: "Error",
          description: "Please select an asset first",
          variant: "destructive"
        });
        return;
      }

      // Validate required fields
      if (!formData.name.trim()) {
        toast({
          title: "Error",
          description: "Please enter a maintenance name",
          variant: "destructive"
        });
        return;
      }

      const requestData: MaintenanceWorkflowRequest = {
        ...formData,
        asset_tag: selectedAsset.asset_id,
        scheduled_date: formData.scheduled_date ? new Date(formData.scheduled_date).toISOString() : undefined
      };

      const response = await maintenanceService.createMaintenanceWorkflowRequest(requestData);
      
      setResult(response);
      setCurrentStep('confirmation');
      
      toast({
        title: "Success",
        description: `Maintenance request created! Asset ${selectedAsset.asset_id} status changed to ${response.asset_status}`,
      });

      if (onSuccess) {
        onSuccess(response);
      }

    } catch (error) {
      console.error('Error creating maintenance request:', error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to create maintenance request",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  const handleClose = () => {
    setCurrentStep('select-asset');
    setSelectedAsset(null);
    setResult(null);
    setFormData({
      asset_tag: '',
      category: 'AV_COMPUTERS',
      name: '',
      request_type: 'preventive',
      priority: 'medium',
      description: '',
      scheduled_date: undefined,
      scheduled_frequency: 'once',
      estimated_hours: undefined,
      estimated_cost: undefined,
      notes: '',
      assigned_technician: ''
    });
    onClose();
  };

  const getStepTitle = () => {
    switch (currentStep) {
      case 'select-asset':
        return 'Step 1: Select Asset for Maintenance';
      case 'create-maintenance':
        return 'Step 2: Create Maintenance Request';
      case 'confirmation':
        return 'Maintenance Request Created Successfully';
      default:
        return 'Create Maintenance Request';
    }
  };

  const renderStepIndicator = () => (
    <div className="flex items-center justify-center space-x-4 mb-6">
      {/* Step 1 */}
      <div className="flex items-center">
        <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
          currentStep === 'select-asset' ? 'bg-blue-600 text-white' :
          ['create-maintenance', 'confirmation'].includes(currentStep) ? 'bg-green-600 text-white' : 
          'bg-gray-300 text-gray-500'
        }`}>
          {['create-maintenance', 'confirmation'].includes(currentStep) ? (
            <CheckCircle className="h-4 w-4" />
          ) : (
            '1'
          )}
        </div>
        <span className="ml-2 text-sm font-medium">Select Asset</span>
      </div>

      <ArrowRight className="h-4 w-4 text-gray-400" />

      {/* Step 2 */}
      <div className="flex items-center">
        <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
          currentStep === 'create-maintenance' ? 'bg-blue-600 text-white' :
          currentStep === 'confirmation' ? 'bg-green-600 text-white' : 
          'bg-gray-300 text-gray-500'
        }`}>
          {currentStep === 'confirmation' ? (
            <CheckCircle className="h-4 w-4" />
          ) : (
            '2'
          )}
        </div>
        <span className="ml-2 text-sm font-medium">Create Request</span>
      </div>

      <ArrowRight className="h-4 w-4 text-gray-400" />

      {/* Step 3 */}
      <div className="flex items-center">
        <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
          currentStep === 'confirmation' ? 'bg-green-600 text-white' : 'bg-gray-300 text-gray-500'
        }`}>
          {currentStep === 'confirmation' ? (
            <CheckCircle className="h-4 w-4" />
          ) : (
            '3'
          )}
        </div>
        <span className="ml-2 text-sm font-medium">Complete</span>
      </div>
    </div>
  );

  const renderSelectAssetStep = () => (
    <div className="space-y-6">
      <div className="text-center">
        <p className="text-gray-600 mb-6">
          Select an asset from the masters/assets table that needs maintenance.
          Only assets with status "Ready" or "Failed" are available for maintenance requests.
        </p>
        
        <Button 
          onClick={() => setIsAssetSelectionOpen(true)}
          size="lg"
          className="bg-blue-600 hover:bg-blue-700"
        >
          Browse Available Assets
        </Button>
      </div>

      {selectedAsset && (
        <Card className="bg-green-50 border-green-200">
          <CardHeader>
            <CardTitle className="text-green-800">Asset Selected</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span className="font-medium">Asset ID:</span> {selectedAsset.asset_id}
              </div>
              <div>
                <span className="font-medium">Type:</span> {selectedAsset.type}
              </div>
              <div>
                <span className="font-medium">Model:</span> {selectedAsset.model || 'N/A'}
              </div>
              <div>
                <span className="font-medium">Location:</span> {selectedAsset.location}
              </div>
              <div>
                <span className="font-medium">Status:</span> 
                <Badge className="ml-2 bg-green-100 text-green-800">{selectedAsset.status}</Badge>
              </div>
              <div>
                <span className="font-medium">Condition:</span> 
                <Badge className="ml-2 bg-blue-100 text-blue-800">{selectedAsset.condition}</Badge>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );

  const renderCreateMaintenanceStep = () => (
    <div className="space-y-4">
      {selectedAsset && (
        <Card className="bg-blue-50 border-blue-200 mb-4">
          <CardContent className="pt-4">
            <div className="flex items-center justify-between">
              <div>
                <span className="font-medium">Selected Asset:</span> {selectedAsset.asset_id} - {selectedAsset.type}
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setCurrentStep('select-asset')}
              >
                Change Asset
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      <div className="grid grid-cols-2 gap-4">
        <div>
          <Label htmlFor="category">Category</Label>
          <Select value={formData.category} onValueChange={(value: any) => setFormData(prev => ({ ...prev, category: value }))}>
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="AV_COMPUTERS">AV Computers</SelectItem>
              <SelectItem value="AV_PRINTERS">AV Printers</SelectItem>
              <SelectItem value="TABS">Tabs</SelectItem>
              <SelectItem value="SCANNERS">Scanners</SelectItem>
              <SelectItem value="POLLPADS">Pollpads</SelectItem>
              <SelectItem value="MONITORS">Monitors</SelectItem>
              <SelectItem value="OTHERS">Others</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div>
          <Label htmlFor="request_type">Request Type</Label>
          <Select value={formData.request_type} onValueChange={(value: any) => setFormData(prev => ({ ...prev, request_type: value }))}>
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="preventive">Preventive</SelectItem>
              <SelectItem value="corrective">Corrective</SelectItem>
              <SelectItem value="emergency">Emergency</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      <div>
        <Label htmlFor="name">Maintenance Name *</Label>
        <Input
          id="name"
          value={formData.name}
          onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
          placeholder="e.g., Routine Software Update, Hardware Repair..."
          required
        />
      </div>

      <div>
        <Label htmlFor="description">Description</Label>
        <Textarea
          id="description"
          value={formData.description}
          onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
          placeholder="Describe the maintenance work needed..."
          rows={3}
        />
      </div>

      <div className="grid grid-cols-2 gap-4">
        <div>
          <Label htmlFor="priority">Priority</Label>
          <Select value={formData.priority} onValueChange={(value: any) => setFormData(prev => ({ ...prev, priority: value }))}>
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="low">Low</SelectItem>
              <SelectItem value="medium">Medium</SelectItem>
              <SelectItem value="high">High</SelectItem>
              <SelectItem value="critical">Critical</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div>
          <Label htmlFor="assigned_technician">Assigned Technician</Label>
          <Input
            id="assigned_technician"
            value={formData.assigned_technician}
            onChange={(e) => setFormData(prev => ({ ...prev, assigned_technician: e.target.value }))}
            placeholder="Technician name..."
          />
        </div>
      </div>

      <div className="grid grid-cols-2 gap-4">
        <div>
          <Label htmlFor="scheduledDate">Scheduled Date</Label>
          <Popover>
            <PopoverTrigger asChild>
              <Button
                variant="outline"
                className={cn(
                  "w-full justify-start text-left font-normal",
                  !formData.scheduled_date && "text-muted-foreground"
                )}
              >
                <CalendarIcon className="mr-2 h-4 w-4" />
                {formData.scheduled_date ? format(new Date(formData.scheduled_date), "PPP") : <span>Pick a date</span>}
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-auto p-0" align="start">
              <Calendar
                mode="single"
                selected={formData.scheduled_date ? new Date(formData.scheduled_date) : undefined}
                onSelect={(date) => setFormData(prev => ({ ...prev, scheduled_date: date }))}
                initialFocus
              />
            </PopoverContent>
          </Popover>
        </div>

        <div>
          <Label htmlFor="scheduled_frequency">Frequency</Label>
          <Select value={formData.scheduled_frequency} onValueChange={(value: any) => setFormData(prev => ({ ...prev, scheduled_frequency: value }))}>
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="once">Once</SelectItem>
              <SelectItem value="weekly">Weekly</SelectItem>
              <SelectItem value="monthly">Monthly</SelectItem>
              <SelectItem value="quarterly">Quarterly</SelectItem>
              <SelectItem value="half-yearly">Half Yearly</SelectItem>
              <SelectItem value="yearly">Yearly</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      <div className="grid grid-cols-2 gap-4">
        <div>
          <Label htmlFor="estimated_hours">Estimated Hours</Label>
          <Input
            id="estimated_hours"
            type="number"
            step="0.5"
            min="0"
            value={formData.estimated_hours || ''}
            onChange={(e) => setFormData(prev => ({ ...prev, estimated_hours: e.target.value ? parseFloat(e.target.value) : undefined }))}
            placeholder="Hours"
          />
        </div>

        <div>
          <Label htmlFor="estimated_cost">Estimated Cost</Label>
          <Input
            id="estimated_cost"
            type="number"
            step="0.01"
            min="0"
            value={formData.estimated_cost || ''}
            onChange={(e) => setFormData(prev => ({ ...prev, estimated_cost: e.target.value ? parseFloat(e.target.value) : undefined }))}
            placeholder="Cost in USD"
          />
        </div>
      </div>

      <div>
        <Label htmlFor="notes">Notes</Label>
        <Textarea
          id="notes"
          value={formData.notes}
          onChange={(e) => setFormData(prev => ({ ...prev, notes: e.target.value }))}
          placeholder="Additional notes..."
          rows={2}
        />
      </div>
    </div>
  );

  const renderConfirmationStep = () => (
    <div className="space-y-6 text-center">
      <div className="flex justify-center">
        <CheckCircle className="h-16 w-16 text-green-600" />
      </div>
      
      <div>
        <h3 className="text-xl font-semibold text-green-800 mb-2">
          Maintenance Request Created Successfully!
        </h3>
        <p className="text-gray-600">
          The maintenance workflow has been initiated and the asset status has been automatically updated.
        </p>
      </div>

      {result && (
        <Card className="bg-green-50 border-green-200">
          <CardHeader>
            <CardTitle className="text-green-800">Workflow Summary</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span className="font-medium">Asset:</span> {result.maintenance.assetTag}
              </div>
              <div>
                <span className="font-medium">New Asset Status:</span>
                <Badge className="ml-2 bg-orange-100 text-orange-800">{result.asset_status}</Badge>
              </div>
              <div>
                <span className="font-medium">Maintenance Type:</span> {result.maintenance.requestType}
              </div>
              <div>
                <span className="font-medium">Priority:</span>
                <Badge className={`ml-2 ${
                  result.maintenance.priority === 'critical' ? 'bg-red-100 text-red-800' :
                  result.maintenance.priority === 'high' ? 'bg-orange-100 text-orange-800' :
                  result.maintenance.priority === 'medium' ? 'bg-yellow-100 text-yellow-800' :
                  'bg-gray-100 text-gray-800'
                }`}>
                  {result.maintenance.priority}
                </Badge>
              </div>
            </div>
            
            <div className="bg-white p-3 rounded border">
              <p className="text-green-800 font-medium mb-1">✅ Workflow Status:</p>
              <p className="text-sm text-gray-600">{result.message}</p>
            </div>
          </CardContent>
        </Card>
      )}

      <div className="bg-blue-50 p-4 rounded-lg">
        <h4 className="font-medium text-blue-800 mb-2">Next Steps:</h4>
        <ul className="text-sm text-blue-700 space-y-1">
          <li>• Asset is now in "Under Maintenance" status</li>
          <li>• Technician can work on the maintenance task</li>
          <li>• Use the maintenance dashboard to track progress</li>
          <li>• Complete the maintenance to change asset status back to "Ready"</li>
        </ul>
      </div>
    </div>
  );

  return (
    <>
      <Dialog open={isOpen} onOpenChange={handleClose}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>{getStepTitle()}</DialogTitle>
          </DialogHeader>

          {renderStepIndicator()}

          <div className="space-y-6">
            {currentStep === 'select-asset' && renderSelectAssetStep()}
            {currentStep === 'create-maintenance' && renderCreateMaintenanceStep()}
            {currentStep === 'confirmation' && renderConfirmationStep()}
          </div>

          <div className="flex justify-between pt-6 border-t">
            <div>
              {currentStep === 'create-maintenance' && (
                <Button
                  variant="outline"
                  onClick={() => setCurrentStep('select-asset')}
                >
                  Back to Asset Selection
                </Button>
              )}
            </div>

            <div className="flex space-x-3">
              <Button variant="outline" onClick={handleClose}>
                {currentStep === 'confirmation' ? 'Close' : 'Cancel'}
              </Button>

              {currentStep === 'select-asset' && selectedAsset && (
                <Button
                  onClick={() => setCurrentStep('create-maintenance')}
                  className="bg-blue-600 hover:bg-blue-700"
                >
                  Continue to Maintenance Details
                </Button>
              )}

              {currentStep === 'create-maintenance' && (
                <Button
                  onClick={handleSubmitMaintenance}
                  disabled={loading || !formData.name.trim()}
                  className="bg-green-600 hover:bg-green-700"
                >
                  {loading ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Creating Request...
                    </>
                  ) : (
                    'Create Maintenance Request'
                  )}
                </Button>
              )}
            </div>
          </div>
        </DialogContent>
      </Dialog>

      <AssetSelectionModal
        isOpen={isAssetSelectionOpen}
        onClose={() => setIsAssetSelectionOpen(false)}
        onAssetSelected={handleAssetSelected}
      />
    </>
  );
}; 