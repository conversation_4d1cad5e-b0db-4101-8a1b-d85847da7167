import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Tabs, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { RefreshCw, AlertTriangle, Calendar, BarChart3 } from 'lucide-react';
import {
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell,
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  <PERSON>Chart,
  Line,
  Legend
} from 'recharts';
import maintenanceService from '@/services/maintenanceService';

export const MaintenanceSimplified: React.FC = () => {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [refreshing, setRefreshing] = useState(false);
  const [stats, setStats] = useState<any>(null);
  const [records, setRecords] = useState<any[]>([]);

  const loadData = async () => {
    try {
      setError(null);
      
      // Try to load real data
      const [statsResponse, recordsResponse] = await Promise.all([
        maintenanceService.getMaintenanceStats().catch(() => null),
        maintenanceService.getMaintenanceRecords().catch(() => ({ data: [] }))
      ]);

      setStats(statsResponse || {
        totalRequests: 0,
        pendingTasks: 0,
        completedLast30Days: 0,
        highPriorityTasks: 0,
        overdueTasks: 0,
        dueThisWeek: 0,
        underMaintenanceCount: 0,
        underMaintenancePercentage: 0,
        statusBreakdown: [],
        priorityBreakdown: [],
        categoryBreakdown: []
      });

      setRecords((recordsResponse as any)?.data || (recordsResponse as any)?.maintenance || []);
      
    } catch (err) {
      console.error('Error loading maintenance data:', err);
      setError('Using offline mode - backend connection failed');
      
      // Fallback data
      setStats({
        totalRequests: 12,
        pendingTasks: 4,
        completedLast30Days: 8,
        highPriorityTasks: 2,
        overdueTasks: 1,
        dueThisWeek: 3,
        underMaintenanceCount: 2,
        underMaintenancePercentage: 16.7,
        statusBreakdown: [
          { status: 'REQUESTED', count: 2 },
          { status: 'IN_PROGRESS', count: 2 },
          { status: 'COMPLETED', count: 8 }
        ],
        priorityBreakdown: [
          { priority: 'high', count: 2 },
          { priority: 'medium', count: 6 },
          { priority: 'low', count: 4 }
        ],
        categoryBreakdown: [
          { category: 'AV_COMPUTERS', count: 5 },
          { category: 'POLLPADS', count: 4 },
          { category: 'TABS', count: 3 }
        ]
      });
      setRecords([]);
    } finally {
      setLoading(false);
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    await loadData();
    setRefreshing(false);
  };

  useEffect(() => {
    loadData();
  }, []);

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <RefreshCw className="h-8 w-8 animate-spin" />
        <span className="ml-2">Loading maintenance data...</span>
      </div>
    );
  }

  // Chart data
  const statusChartData = stats?.statusBreakdown?.map((item: any) => ({
    name: item.status?.replace('_', ' ') || 'Unknown',
    value: item.count || 0,
    color: {
      'REQUESTED': '#f59e0b',
      'SCHEDULED': '#3b82f6', 
      'IN_PROGRESS': '#8b5cf6',
      'TESTING': '#06b6d4',
      'COMPLETED': '#10b981'
    }[item.status] || '#64748b'
  })) || [];

  const categoryChartData = stats?.categoryBreakdown?.map((item: any) => ({
    name: item.category?.replace('_', ' ') || 'Unknown',
    value: item.count || 0
  })) || [];

  return (
    <div className="pt-2 pb-6 px-4 space-y-4">
      {/* Header */}
      <div className="flex justify-between items-center mb-2">
        <div>
          <h2 className="text-2xl font-bold">Maintenance Management</h2>
          {error && (
            <div className="flex items-center mt-1 text-amber-600">
              <AlertTriangle className="h-4 w-4 mr-1" />
              <span className="text-sm">{error}</span>
            </div>
          )}
        </div>
        <Button 
          onClick={handleRefresh} 
          disabled={refreshing}
          variant="outline"
          size="sm"
        >
          <RefreshCw className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
          {refreshing ? 'Refreshing...' : 'Refresh'}
        </Button>
      </div>

      {/* Main Tabs */}
      <Tabs defaultValue="analytics" className="w-full">
        <TabsList className="grid w-full grid-cols-2 mb-2">
          <TabsTrigger value="analytics" className="flex items-center space-x-2">
            <BarChart3 size={16} />
            <span>Analytics</span>
          </TabsTrigger>
          <TabsTrigger value="schedule" className="flex items-center space-x-2">
            <Calendar size={16} />
            <span>Schedule</span>
          </TabsTrigger>
        </TabsList>

        {/* Analytics Tab */}
        <TabsContent value="analytics" className="space-y-6">
          {/* Summary Cards */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm">Total Requests</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-blue-600">{stats?.totalRequests || 0}</div>
                <p className="text-xs text-gray-500 mt-1">All maintenance</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm">Pending Tasks</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-amber-600">{stats?.pendingTasks || 0}</div>
                <p className="text-xs text-gray-500 mt-1">Awaiting completion</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm">Completed</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-green-600">{stats?.completedLast30Days || 0}</div>
                <p className="text-xs text-gray-500 mt-1">Last 30 days</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm">High Priority</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-red-600">{stats?.highPriorityTasks || 0}</div>
                <p className="text-xs text-gray-500 mt-1">Urgent tasks</p>
              </CardContent>
            </Card>
          </div>

          {/* Charts Row */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Status Distribution */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Status Distribution</CardTitle>
                <CardDescription>Current maintenance status breakdown</CardDescription>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <PieChart>
                    <Pie
                      data={statusChartData.filter(d => d.value > 0)}
                      cx="50%"
                      cy="50%"
                      labelLine={false}
                      label={({ name, percent, value }) =>
                        value > 0 ? `${name} ${(percent * 100).toFixed(0)}%` : null
                      }
                      outerRadius={100}
                      fill="#8884d8"
                      dataKey="value"
                    >
                      {statusChartData.filter(d => d.value > 0).map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={entry.color} />
                      ))}
                    </Pie>
                    <Tooltip />
                  </PieChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            {/* Category Distribution */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Equipment Categories</CardTitle>
                <CardDescription>Maintenance by asset type</CardDescription>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <BarChart data={categoryChartData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="name" />
                    <YAxis allowDecimals={false} />
                    <Tooltip />
                    <Bar dataKey="value" fill="#3b82f6" />
                  </BarChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </div>

          {/* Additional Metrics */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm">Under Maintenance</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-xl font-bold text-orange-600">
                  {stats?.underMaintenanceCount || 0}
                </div>
                <p className="text-xs text-gray-500 mt-1">
                  {stats?.underMaintenancePercentage?.toFixed(1) || 0}% of total assets
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm">Due This Week</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-xl font-bold text-purple-600">{stats?.dueThisWeek || 0}</div>
                <p className="text-xs text-gray-500 mt-1">Scheduled soon</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm">Overdue</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-xl font-bold text-red-600">{stats?.overdueTasks || 0}</div>
                <p className="text-xs text-gray-500 mt-1">Past due date</p>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Schedule Tab */}
        <TabsContent value="schedule" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Maintenance Schedule</CardTitle>
              <CardDescription>Upcoming and current maintenance tasks</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8 text-gray-500">
                <Calendar className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                <p>Schedule view with {records.length} maintenance records</p>
                <p className="text-sm mt-2">
                  {error ? 'Connect to backend to view full schedule' : 'Schedule features coming soon'}
                </p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Debug Info */}
      <div className="mt-4 p-3 bg-gray-50 rounded text-xs text-gray-600">
        🔧 Status: {error ? 'Offline Mode' : 'Connected'} | 
        📊 Records: {records.length} | 
        📡 Backend: {error ? 'Disconnected' : 'Connected'}
      </div>
    </div>
  );
}; 