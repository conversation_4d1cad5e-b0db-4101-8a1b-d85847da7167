import React, { useState, useEffect } from 'react';
import { AppLayout } from "@/components/layout/AppLayout";
import { <PERSON>, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { UserCircle, Download, Clock, Activity, Loader2, AlertCircle } from 'lucide-react';
import { ReportActions } from "@/components/reports/ReportActions";

interface UserActivity {
  id: string;
  userId: string;
  userName: string;
  userEmail: string;
  activityType: string;
  action: string;
  module: string;
  resourceType?: string;
  resourceId?: string;
  resourceName?: string;
  ipAddress?: string;
  userAgent?: string;
  status: string;
  details?: string;
  timestamp: string;
}

interface PaginationInfo {
  total: number;
  limit: number;
  offset: number;
  hasMore: boolean;
}

// Removed mock data generation - now using real backend data

export default function UserActivityPage() {
  const [activities, setActivities] = useState<UserActivity[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [moduleFilter, setModuleFilter] = useState('all');
  const [activityTypeFilter, setActivityTypeFilter] = useState('all');
  const [pagination, setPagination] = useState<PaginationInfo>({
    total: 0,
    limit: 50,
    offset: 0,
    hasMore: false
  });

  useEffect(() => {
    fetchActivities();
  }, [searchTerm, moduleFilter, activityTypeFilter, pagination.offset]);

  const fetchActivities = async () => {
    try {
      setLoading(true);
      setError(null);

      const params = new URLSearchParams({
        limit: pagination.limit.toString(),
        offset: pagination.offset.toString(),
      });

      if (moduleFilter !== 'all') {
        params.append('module', moduleFilter);
      }

      if (activityTypeFilter !== 'all') {
        params.append('activity_type', activityTypeFilter);
      }

      const response = await fetch(`http://localhost:8000/api/reports/audit/user-activity?${params}`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error('Failed to fetch user activities');
      }

      const result = await response.json();
      if (result.success) {
        setActivities(result.data.activities);
        setPagination(result.data.pagination);
      } else {
        throw new Error('Invalid response format');
      }
    } catch (err) {
      console.error('Error fetching user activities:', err);
      setError(err instanceof Error ? err.message : 'Failed to load user activities');
    } finally {
      setLoading(false);
    }
  };

  const handleSearch = (value: string) => {
    setSearchTerm(value);
    setPagination(prev => ({ ...prev, offset: 0 }));
  };

  const handleModuleFilter = (value: string) => {
    setModuleFilter(value);
    setPagination(prev => ({ ...prev, offset: 0 }));
  };

  const handleActivityTypeFilter = (value: string) => {
    setActivityTypeFilter(value);
    setPagination(prev => ({ ...prev, offset: 0 }));
  };

  const handleLoadMore = () => {
    setPagination(prev => ({ ...prev, offset: prev.offset + prev.limit }));
  };

  // Filter activities locally for search term
  const filteredActivities = activities.filter(activity => {
    if (!searchTerm) return true;
    const searchLower = searchTerm.toLowerCase();
    return (
      activity.userName.toLowerCase().includes(searchLower) ||
      activity.action.toLowerCase().includes(searchLower) ||
      activity.details?.toLowerCase().includes(searchLower) ||
      activity.module.toLowerCase().includes(searchLower)
    );
  });

  if (loading && activities.length === 0) {
    return (
      <AppLayout>
        <div className="flex items-center justify-center h-64">
          <Loader2 className="h-8 w-8 animate-spin" />
          <span className="ml-2">Loading user activities...</span>
        </div>
      </AppLayout>
    );
  }

  if (error) {
    return (
      <AppLayout>
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <AlertCircle className="h-8 w-8 text-red-500 mx-auto mb-4" />
            <p className="text-red-600 mb-4">{error}</p>
            <Button onClick={fetchActivities}>Retry</Button>
          </div>
        </div>
      </AppLayout>
    );
  }

  return (
    <AppLayout>
      <div className="container mx-auto py-6">
        <div className="mb-6">
          <h1 className="text-3xl font-bold">User Activity Logs</h1>
          <p className="text-muted-foreground">Monitor and track user activities across the system</p>
        </div>

        <Card className="mb-6">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Activity className="h-5 w-5" />
              User Activity Analysis
            </CardTitle>
            <CardDescription>
              View and analyze user actions and system interactions ({pagination.total} total entries)
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
              <Input
                placeholder="Search by user, action, or details..."
                value={searchTerm}
                onChange={(e) => handleSearch(e.target.value)}
                className="w-full"
              />
              <Select value={moduleFilter} onValueChange={handleModuleFilter}>
                <SelectTrigger>
                  <SelectValue placeholder="Filter by module" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Modules</SelectItem>
                  <SelectItem value="Authentication">Authentication</SelectItem>
                  <SelectItem value="Asset Management">Asset Management</SelectItem>
                  <SelectItem value="Reports">Reports</SelectItem>
                  <SelectItem value="Settings">Settings</SelectItem>
                  <SelectItem value="Maintenance">Maintenance</SelectItem>
                  <SelectItem value="User Management">User Management</SelectItem>
                </SelectContent>
              </Select>
              <Select value={activityTypeFilter} onValueChange={handleActivityTypeFilter}>
                <SelectTrigger>
                  <SelectValue placeholder="Filter by activity type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Types</SelectItem>
                  <SelectItem value="login">Login</SelectItem>
                  <SelectItem value="logout">Logout</SelectItem>
                  <SelectItem value="create">Create</SelectItem>
                  <SelectItem value="update">Update</SelectItem>
                  <SelectItem value="delete">Delete</SelectItem>
                  <SelectItem value="view">View</SelectItem>
                  <SelectItem value="export">Export</SelectItem>
                </SelectContent>
              </Select>
              <ReportActions 
                onDownload={(format) => {
                  console.log(`Downloading activity log in ${format} format`);
                  const csvContent = [
                    ['User ID', 'User Name', 'Action', 'Module', 'Details', 'Timestamp', 'IP Address'].join(','),
                    ...filteredActivities.map(activity => [
                      activity.userId,
                      activity.userName,
                      activity.action,
                      activity.module,
                      activity.details,
                      new Date(activity.timestamp).toLocaleString(),
                      activity.ipAddress
                    ].join(','))
                  ].join('\n');

                  const blob = new Blob([csvContent], { type: 'text/csv' });
                  const url = window.URL.createObjectURL(blob);
                  const a = document.createElement('a');
                  a.href = url;
                  a.download = `user-activity-log-${new Date().toISOString().split('T')[0]}.${format.toLowerCase()}`;
                  a.click();
                  window.URL.revokeObjectURL(url);
                }}
                onPrint={() => {
                  console.log('Printing activity log');
                  window.print();
                }}
              />
            </div>

            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>User</TableHead>
                    <TableHead>Action</TableHead>
                    <TableHead>Module</TableHead>
                    <TableHead>Details</TableHead>
                    <TableHead>Timestamp</TableHead>
                    <TableHead>IP Address</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredActivities.map((activity) => (
                    <TableRow key={activity.id}>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <UserCircle className="h-4 w-4" />
                          <span className="font-medium">{activity.userName}</span>
                        </div>
                      </TableCell>
                      <TableCell>{activity.action}</TableCell>
                      <TableCell>{activity.module}</TableCell>
                      <TableCell>{activity.details}</TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <Clock className="h-4 w-4 text-muted-foreground" />
                          {new Date(activity.timestamp).toLocaleString()}
                        </div>
                      </TableCell>
                      <TableCell className="font-mono">{activity.ipAddress}</TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>

            {pagination.hasMore && (
              <div className="flex justify-center mt-4">
                <Button
                  onClick={handleLoadMore}
                  disabled={loading}
                  variant="outline"
                >
                  {loading ? (
                    <>
                      <Loader2 className="h-4 w-4 animate-spin mr-2" />
                      Loading...
                    </>
                  ) : (
                    'Load More'
                  )}
                </Button>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </AppLayout>
  );
}
