import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Textarea } from '@/components/ui/textarea';
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Star, Edit, Search, Calendar, BarChart3, Plus, X, Trash2, RefreshCw, AlertTriangle, Eye } from 'lucide-react';
import { 
  <PERSON><PERSON><PERSON>, 
  <PERSON>, 
  XAxis, 
  <PERSON>A<PERSON>s, 
  CartesianGrid, 
  Tooltip, 
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell,
  BarChart,
  Bar,
  Legend
} from 'recharts';
import maintenanceService, { 
  Maintenance, 
  MaintenanceStats, 
  MaintenanceFilters 
} from '@/services/maintenanceService';

// Now using maintenance service from '@/services/maintenanceService'
// Types are imported from the service

export const MaintenanceTab: React.FC = () => {
  // State management
  const [maintenanceRecords, setMaintenanceRecords] = useState<Maintenance[]>([]);
  const [maintenanceStats, setMaintenanceStats] = useState<MaintenanceStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [refreshing, setRefreshing] = useState(false);

  // Filter states
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string | 'ALL'>('ALL');
  const [priorityFilter, setPriorityFilter] = useState<string | 'ALL'>('ALL');
  const [categoryFilter, setCategoryFilter] = useState<string | 'ALL'>('ALL');

  // Modal states
  const [selectedRequest, setSelectedRequest] = useState<Maintenance | null>(null);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isNewRequestModalOpen, setIsNewRequestModalOpen] = useState(false);
  const [viewedRequest, setViewedRequest] = useState<Maintenance | null>(null);
  const [isViewModalOpen, setIsViewModalOpen] = useState(false);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [requestToDelete, setRequestToDelete] = useState<Maintenance | null>(null);

  // Form states
  const [newRequest, setNewRequest] = useState<{
    assetTags: string[]; // changed from assetTag: string
    category: string;
    name: string;
    requestType: 'preventive' | 'corrective' | 'emergency';
    priority: 'low' | 'medium' | 'high' | 'critical';
    assignedTechnician: string;
    description: string;
    scheduledDate: string;
    estimatedHours: number;
    estimatedCost: number;
    notes: string;
    scheduledFrequency: string;
  }>({
    assetTags: [],
    category: '',
    name: '',
    requestType: 'preventive',
    priority: 'medium',
    assignedTechnician: '',
    description: '',
    scheduledDate: '',
    estimatedHours: 1,
    estimatedCost: 0,
    notes: '',
    scheduledFrequency: '',
  });

  const [newRequestError, setNewRequestError] = useState<string | null>(null);

  const [editRequest, setEditRequest] = useState<Partial<Maintenance>>({});

  // Dummy technicians list (replace with API call if needed)
  const technicians = [
    { id: 'tech1', name: 'Alice Johnson' },
    { id: 'tech2', name: 'Bob Smith' },
    { id: 'tech3', name: 'Charlie Lee' },
  ];

  // Asset tag options (replace with API call if needed)
  const assetTagOptions = [
    'Asset-001',
    'Asset-002',
    'Asset-003',
    'Asset-004',
    'Asset-005',
    'Asset-006',
    'Asset-007',
    'Asset-008',
  ];
  const [assetTagSearch, setAssetTagSearch] = useState('');
  const [assetTagDropdownOpen, setAssetTagDropdownOpen] = useState(false);
  const filteredAssetTags = assetTagOptions.filter(tag => tag.toLowerCase().includes(assetTagSearch.toLowerCase()));

  // Loading states for actions
  const [actionLoading, setActionLoading] = useState<{
    create: boolean;
    update: boolean;
    delete: boolean;
  }>({ create: false, update: false, delete: false });

  // Load all maintenance data
  const loadMaintenanceData = async (showLoading = true) => {
    try {
      if (showLoading) setLoading(true);
      setError(null);

      const [recordsResponse, stats] = await Promise.all([
        maintenanceService.getMaintenanceRecords(),
        maintenanceService.getMaintenanceStats()
      ]);

      setMaintenanceRecords(recordsResponse.data || recordsResponse.maintenance || []);
      setMaintenanceStats(stats);
    } catch (err) {
      console.error('Error loading maintenance data:', err);
      setError(err instanceof Error ? err.message : 'Failed to load maintenance data');
    } finally {
      setLoading(false);
    }
  };

  // Refresh data
  const handleRefresh = async () => {
    setRefreshing(true);
    await loadMaintenanceData(false);
    setRefreshing(false);
  };

  // CREATE Operation
  const handleCreateRequest = async () => {
    setActionLoading(prev => ({ ...prev, create: true }));
    setNewRequestError(null);
    const results: string[] = [];
    try {
      for (const assetTag of newRequest.assetTags) {
        const payload = {
          assetTag,
          category: newRequest.category,
          name: newRequest.name,
          requestType: newRequest.requestType,
          priority: newRequest.priority,
          assignedTechnician: newRequest.assignedTechnician,
          description: newRequest.description,
          scheduledDate: newRequest.scheduledDate,
          ...(newRequest.scheduledFrequency ? { scheduledFrequency: newRequest.scheduledFrequency } : {}),
          status: 'REQUESTED',
          estimatedHours: newRequest.estimatedHours,
          estimatedCost: newRequest.estimatedCost,
          notes: newRequest.notes,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        };
        try {
          await maintenanceService.createMaintenanceRequest(payload as any);
          results.push(`${assetTag}: Success`);
        } catch (error: any) {
          let errorMsg = '';
          if (error.response) {
            try {
              const errorText = await error.response.text?.();
              if (error.response.status === 400 && errorText && errorText.toLowerCase().includes('duplicate')) {
                errorMsg = 'Asset tag is already entered.';
              } else {
                errorMsg = `HTTP ${error.response.status} ${error.response.statusText}: ${errorText}`;
              }
            } catch (e) {
              errorMsg = `HTTP ${error.response.status} ${error.response.statusText}`;
            }
          } else if (error instanceof Error) {
            if (error.message && error.message.toLowerCase().includes('duplicate')) {
              errorMsg = 'Asset tag is already entered.';
            } else {
              errorMsg = error.message;
            }
          } else {
            errorMsg = JSON.stringify(error);
          }
          results.push(`${assetTag}: ${errorMsg}`);
        }
      }
      setNewRequestError(results.join('\n'));
      setIsNewRequestModalOpen(false);
      resetNewRequestForm();
      await loadMaintenanceData(false);
    } finally {
      setActionLoading(prev => ({ ...prev, create: false }));
    }
  };

  // UPDATE Operation
  const handleUpdateRequest = async () => {
    if (!selectedRequest?.assetTag) return;
    setActionLoading(prev => ({ ...prev, update: true }));
    try {
      const updatedRequest = await MaintenanceAPI.updateMaintenance(
        selectedRequest.assetTag,
        { ...editRequest, updatedAt: new Date().toISOString() }
      );

      if (updatedRequest) {
        await loadMaintenanceData(false);
        setIsEditModalOpen(false);
        setSelectedRequest(null);
        setEditRequest({});
      }
    } catch (error) {
      console.error('Error updating maintenance request:', error);
      setError('Failed to update maintenance request');
    } finally {
      setActionLoading(prev => ({ ...prev, update: false }));
    }
  };

  // DELETE Operation
  const handleDeleteRequest = async () => {
    if (!requestToDelete?.assetTag) return;
    setActionLoading(prev => ({ ...prev, delete: true }));
    try {
      const success = await MaintenanceAPI.deleteMaintenance(requestToDelete.assetTag);
      if (success) {
        await loadMaintenanceData(false);
        setIsDeleteModalOpen(false);
        setRequestToDelete(null);
      }
    } catch (error) {
      console.error('Error deleting maintenance request:', error);
      setError('Failed to delete maintenance request');
    } finally {
      setActionLoading(prev => ({ ...prev, delete: false }));
    }
  };

  // Status Update Operation
  const handleStatusUpdate = async (id: string, newStatus: string) => {
    try {
      const updatedRequest = await MaintenanceAPI.updateMaintenanceStatus(id, newStatus);
      
      if (updatedRequest) {
        await loadMaintenanceData(false);
      }
    } catch (error) {
      console.error('Error updating status:', error);
      setError('Failed to update status');
    }
  };

  // Helper functions
  const resetNewRequestForm = () => {
    setNewRequest({
      assetTags: [],
      category: '',
      name: '',
      requestType: 'preventive',
      priority: 'medium',
      assignedTechnician: '',
      description: '',
      scheduledDate: '',
      estimatedHours: 1,
      estimatedCost: 0,
      notes: '',
      scheduledFrequency: '',
    });
  };

  const openEditModal = (request: Maintenance) => {
    setSelectedRequest(request);
    setEditRequest({ ...request });
    setIsEditModalOpen(true);
  };

  const openDeleteModal = (request: Maintenance) => {
    setRequestToDelete(request);
    setIsDeleteModalOpen(true);
  };

  // View request handler
  const handleViewRequest = (request: Maintenance) => {
    setViewedRequest(request);
    setIsViewModalOpen(true);
  };

  // Filter maintenance records
  const filteredRequests = maintenanceRecords.filter(request => {
    const matchesSearch = [
      request.description,
      request.notes,
      request.assetTag,
      request.name,
      request.assignedTechnician
    ].some(field => field?.toLowerCase().includes(searchTerm.toLowerCase()));

    const matchesStatus = statusFilter === 'ALL' || request.status === statusFilter;
    const matchesPriority = priorityFilter === 'ALL' || request.priority === priorityFilter;
    const matchesCategory = categoryFilter === 'ALL' || request.category === categoryFilter;

    return matchesSearch && matchesStatus && matchesPriority && matchesCategory;
  });

  // Generate chart data
  const generateChartData = () => {
    const statusData = ['REQUESTED', 'SCHEDULED', 'IN_PROGRESS', 'TESTING', 'COMPLETED'].map(status => ({
      name: status.replace('_', ' '),
      value: maintenanceRecords.filter(req => req.status === status).length,
      color: {
        'REQUESTED': '#f59e0b',
        'SCHEDULED': '#3b82f6',
        'IN_PROGRESS': '#8b5cf6',
        'TESTING': '#06b6d4',
        'COMPLETED': '#10b981'
      }[status]
    }));

    const priorityData = ['low', 'medium', 'high', 'critical'].map(priority => ({
      name: priority.charAt(0).toUpperCase() + priority.slice(1),
      value: maintenanceRecords.filter(req => req.priority === priority).length,
      color: {
        'low': '#16a34a',
        'medium': '#ca8a04',
        'high': '#ea580c',
        'critical': '#dc2626'
      }[priority]
    }));

    return { statusData, priorityData };
  };

  const { statusData, priorityData } = generateChartData();

  // Generate line chart data for the last 30 days
  const lineChartData = React.useMemo(() => {
    const days = 30;
    const today = new Date();
    const data = [];
    for (let i = days - 1; i >= 0; i--) {
      const date = new Date(today);
      date.setDate(today.getDate() - i);
      const dateStr = date.toISOString().slice(0, 10);

      const completed = maintenanceRecords.filter(req =>
        req.status === 'COMPLETED' &&
        req.updatedAt &&
        req.updatedAt.slice(0, 10) === dateStr
      ).length;

      const scheduled = maintenanceRecords.filter(req =>
        req.scheduledDate &&
        req.scheduledDate.slice(0, 10) === dateStr
      ).length;

      data.push({
        date: dateStr,
        completed,
        scheduled,
      });
    }
    return data;
  }, [maintenanceRecords]);

  // Load data on mount
  useEffect(() => {
    loadMaintenanceData();
  }, []);

  // Loading state
  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="text-center">
          <RefreshCw className="h-8 w-8 animate-spin mx-auto mb-4 text-blue-500" />
          <p className="text-gray-600">Loading maintenance data...</p>
        </div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="text-center">
          <AlertTriangle className="h-8 w-8 mx-auto mb-4 text-red-500" />
          <p className="text-red-600 mb-4">{error}</p>
          <Button onClick={() => loadMaintenanceData()}>
            <RefreshCw className="h-4 w-4 mr-2" />
            Retry
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="pt-2 pb-6 px-4 space-y-4">
      {/* Header - tighter spacing */}
      <div className="flex justify-end items-center mb-2">
        <Button 
          onClick={handleRefresh} 
          disabled={refreshing}
          variant="outline"
          size="sm"
        >
          <RefreshCw className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
          {refreshing ? 'Refreshing...' : 'Refresh'}
        </Button>
      </div>
      {/* Main Tabs - less vertical gap */}
      <Tabs defaultValue="schedule" className="w-full">
        <TabsList className="grid w-full grid-cols-2 mb-2">
          <TabsTrigger value="schedule" className="flex items-center space-x-2">
            <Calendar size={16} />
            <span>Maintenance Schedule</span>
          </TabsTrigger>
          <TabsTrigger value="analytics" className="flex items-center space-x-2">
            <BarChart3 size={16} />
            <span>Analytics</span>
          </TabsTrigger>
        </TabsList>

        {/* Maintenance Schedule Tab */}
        <TabsContent value="schedule" className="space-y-6">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <div>
                <CardTitle>Maintenance Schedule</CardTitle>
                <CardDescription>Preventive maintenance requests with filters</CardDescription>
              </div>
              <Button 
                onClick={() => setIsNewRequestModalOpen(true)}
                className="flex items-center space-x-2"
              >
                <Plus size={16} />
                <span>New Request</span>
              </Button>
            </CardHeader>
            <CardContent>
              {/* Filters and Search */}
              <div className="grid grid-cols-1 md:grid-cols-5 gap-4 mb-6">
                <div className="relative">
                  <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                  <Input
                    placeholder="Search maintenance requests..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>
                <Select value={categoryFilter} onValueChange={(value) => setCategoryFilter(value as string | 'ALL')}>
                  <SelectTrigger>
                    <SelectValue placeholder="Category" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="ALL">All Categories</SelectItem>
                    <SelectItem value="AV_COMPUTER">Computer</SelectItem>
                    <SelectItem value="AV_PRINTER">Printer</SelectItem>
                    <SelectItem value="POLLPAD">Poll Pad</SelectItem>
                    <SelectItem value="BMD">BMD</SelectItem>
                    <SelectItem value="TABULATOR">Tabulator</SelectItem>
                  </SelectContent>
                </Select>
                <Select value={statusFilter} onValueChange={(value) => setStatusFilter(value as string | 'ALL')}>
                  <SelectTrigger>
                    <SelectValue placeholder="Status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="ALL">All Statuses</SelectItem>
                    <SelectItem value="REQUESTED">Requested</SelectItem>
                    <SelectItem value="SCHEDULED">Scheduled</SelectItem>
                    <SelectItem value="IN_PROGRESS">In Progress</SelectItem>
                    <SelectItem value="TESTING">Testing</SelectItem>
                    <SelectItem value="COMPLETED">Completed</SelectItem>
                  </SelectContent>
                </Select>
                <Select value={priorityFilter} onValueChange={(value) => setPriorityFilter(value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="Priority" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="ALL">All Priorities</SelectItem>
                    <SelectItem value="critical">critical</SelectItem>
                    <SelectItem value="high">high</SelectItem>
                    <SelectItem value="medium">medium</SelectItem>
                    <SelectItem value="low">low</SelectItem>
                  </SelectContent>
                </Select>
                <div className="text-sm text-gray-600 flex items-center">
                  Showing {filteredRequests.length} of {maintenanceRecords.length} requests
                </div>
              </div>

              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>asset_tag</TableHead>
                    <TableHead>category</TableHead>
                    <TableHead>name</TableHead>
                    <TableHead>priority</TableHead>
                    <TableHead>status</TableHead>
                    <TableHead>description</TableHead>
                    <TableHead>assigned_technician</TableHead>
                    <TableHead>scheduled_date</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredRequests.map(request => (
                    <TableRow key={request.assetTag}>
                      <TableCell>{request.assetTag}</TableCell>
                      <TableCell>{request.category || 'Unknown'}</TableCell>
                      <TableCell>{request.name || 'Unknown'}</TableCell>
                      <TableCell>{request.priority}</TableCell>
                      <TableCell>{request.status}</TableCell>
                      <TableCell>{request.description || 'None'}</TableCell>
                      <TableCell>{request.assignedTechnician || 'Unassigned'}</TableCell>
                      <TableCell>{request.scheduledDate ? new Date(request.scheduledDate).toLocaleDateString() : 'N/A'}</TableCell>
                      <TableCell>
                        <div className="flex items-center space-x-2">
                          <Button size="sm" onClick={() => handleViewRequest(request)} variant="ghost">
                            <Eye className="w-4 h-4" />
                          </Button>
                          <Button size="sm" variant="outline" onClick={() => openEditModal(request)} className="ml-2">
                            <Edit className="w-4 h-4" />
                          </Button>
                          <Button size="sm" variant="destructive" onClick={() => openDeleteModal(request)} className="ml-2">
                            <Trash2 className="w-4 h-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>

              {filteredRequests.length === 0 && (
                <div className="text-center py-8 text-gray-500">
                  <p>No preventive maintenance requests found.</p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Analytics Tab */}
        <TabsContent value="analytics" className="space-y-6">
          {/* Summary Cards in Horizontal Row */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Total Requests</CardTitle>
                <CardDescription>All preventive maintenance</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-3xl font-bold text-blue-600">{maintenanceRecords.length}</div>
                <p className="text-sm text-gray-500 mt-2">Total requests</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Pending Tasks</CardTitle>
                <CardDescription>Not yet completed</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-3xl font-bold text-amber-600">
                  {maintenanceRecords.filter(req => req.status !== 'COMPLETED').length}
                </div>
                <p className="text-sm text-gray-500 mt-2">Awaiting completion</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Completed</CardTitle>
                <CardDescription>Last 30 days</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-3xl font-bold text-green-600">
                  {maintenanceRecords.filter(req => {
                    const thirtyDaysAgo = new Date();
                    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
                    return req.status === 'COMPLETED' && new Date(req.updatedAt) > thirtyDaysAgo;
                  }).length}
                </div>
                <p className="text-sm text-gray-500 mt-2">Recently completed</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>High Priority</CardTitle>
                <CardDescription>Critical & high priority</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-3xl font-bold text-red-600">
                  {maintenanceRecords.filter(req => 
                    req.priority === 'critical' || req.priority === 'high'
                  ).length}
                </div>
                <p className="text-sm text-gray-500 mt-2">Urgent tasks</p>
              </CardContent>
            </Card>
          </div>

          {/* Charts Section */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Line Chart - Maintenance Trends */}
            <Card>
              <CardHeader>
                <CardTitle>Maintenance Trends</CardTitle>
                <CardDescription>Completed vs Scheduled over last 30 days</CardDescription>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <LineChart data={lineChartData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="date" />
                    <YAxis />
                    <Tooltip />
                    <Legend />
                    <Line 
                      type="monotone" 
                      dataKey="completed" 
                      stroke="#10b981" 
                      strokeWidth={2}
                      name="Completed"
                    />
                    <Line 
                      type="monotone" 
                      dataKey="scheduled" 
                      stroke="#3b82f6" 
                      strokeWidth={2}
                      name="Scheduled"
                    />
                  </LineChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            {/* Pie Chart - Status Distribution */}
            <Card>
              <CardHeader>
                <CardTitle>Status Distribution</CardTitle>
                <CardDescription>Current maintenance request statuses</CardDescription>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <PieChart>
                    <Pie
                      data={statusData.filter(d => d.value > 0)}
                      cx="50%"
                      cy="50%"
                      labelLine={false}
                      label={({ name, percent, value }) =>
                        value > 0 ? (
                          <tspan style={{
                            fontSize: 18,
                            fontWeight: 600,
                            fill: statusData.find(d => d.name === name)?.color || '#333',
                            textShadow: '0 1px 2px #fff',
                          }}>{`${name} ${(percent * 100).toFixed(0)}%`}</tspan>
                        ) : null
                      }
                      outerRadius={100}
                      fill="#8884d8"
                      dataKey="value"
                    >
                      {statusData.filter(d => d.value > 0).map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={entry.color} />
                      ))}
                    </Pie>
                    <Tooltip />
                  </PieChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            {/* Bar Chart - Priority Distribution */}
            <Card>
              <CardHeader>
                <CardTitle>Priority Distribution</CardTitle>
                <CardDescription>Maintenance requests by priority level</CardDescription>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <BarChart data={priorityData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="name" />
                    <YAxis />
                    <Tooltip />
                    <Bar dataKey="value" fill="#8884d8">
                      {priorityData.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={entry.color} />
                      ))}
                    </Bar>
                  </BarChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            {/* Asset Type Distribution */}
            <Card>
              <CardHeader>
                <CardTitle>Asset Type Maintenance</CardTitle>
                <CardDescription>Requests by equipment type</CardDescription>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <BarChart data={[
                    { name: 'AV Computers', value: maintenanceRecords.filter(req => req.category === 'AV computers').length, color: '#3b82f6' },
                    { name: 'AV Printers', value: maintenanceRecords.filter(req => req.category === 'AV printers').length, color: '#f59e0b' },
                    { name: 'Pollpads', value: maintenanceRecords.filter(req => req.category === 'pollpads').length, color: '#06b6d4' },
                    { name: 'Tabs', value: maintenanceRecords.filter(req => req.category === 'tabs').length, color: '#8b5cf6' },
                    { name: 'Scanners', value: maintenanceRecords.filter(req => req.category === 'scanners').length, color: '#10b981' },
                    { name: 'Monitors', value: maintenanceRecords.filter(req => req.category === 'monitors').length, color: '#ef4444' },
                    { name: 'Others', value: maintenanceRecords.filter(req => req.category === 'others').length, color: '#64748b' },
                  ].filter(item => item.value > 0)}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="name" />
                    <YAxis allowDecimals={false} />
                    <Tooltip />
                    <Bar dataKey="value">
                      {[
                        { name: 'AV Computers', color: '#3b82f6' },
                        { name: 'AV Printers', color: '#f59e0b' },
                        { name: 'Pollpads', color: '#06b6d4' },
                        { name: 'Tabs', color: '#8b5cf6' },
                        { name: 'Scanners', color: '#10b981' },
                        { name: 'Monitors', color: '#ef4444' },
                        { name: 'Others', color: '#64748b' },
                      ].map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={entry.color} />
                      ))}
                    </Bar>
                  </BarChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>

      {/* New Maintenance Request Modal */}
      <Dialog open={isNewRequestModalOpen} onOpenChange={setIsNewRequestModalOpen}>
        <DialogContent className="sm:max-w-[600px] max-h-[90vh] flex flex-col">
          <DialogHeader className="flex flex-row items-center justify-between space-y-0 flex-shrink-0">
            <DialogTitle>New Maintenance Request</DialogTitle>
          </DialogHeader>

          <div className="flex-1 overflow-y-auto px-1">
            {newRequestError && (
              <div className="mb-2 text-red-600 text-sm font-medium text-center">{newRequestError}</div>
            )}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 py-4">
              {/* Asset Dropdown - Multi-select Combobox with typing and checkboxes */}
              <div className="space-y-2">
                <label className="text-sm font-medium">asset_tag(s)</label>
                <div className="relative">
                  <div className="flex flex-wrap gap-1 mb-1">
                    {newRequest.assetTags.map(tag => (
                      <span key={tag} className="bg-blue-100 text-blue-800 px-2 py-0.5 rounded text-xs flex items-center">
                        {tag}
                        <button
                          type="button"
                          className="ml-1 text-blue-500 hover:text-red-500"
                          onClick={() => setNewRequest(prev => ({ ...prev, assetTags: prev.assetTags.filter(t => t !== tag) }))}
                          aria-label={`Remove ${tag}`}
                        >
                          ×
                        </button>
                      </span>
                    ))}
                  </div>
                  <input
                    type="text"
                    placeholder="Type or select asset tag"
                    value={assetTagSearch}
                    onFocus={() => setAssetTagDropdownOpen(true)}
                    onBlur={() => setTimeout(() => setAssetTagDropdownOpen(false), 150)}
                    onChange={e => {
                      setAssetTagSearch(e.target.value);
                      setAssetTagDropdownOpen(true);
                    }}
                    className="h-10 w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                  {assetTagDropdownOpen && (
                    <div className="absolute z-10 bg-white border border-gray-200 rounded shadow w-full mt-1 max-h-40 overflow-y-auto">
                      {(assetTagSearch.length > 0 ? filteredAssetTags : assetTagOptions).length === 0 ? (
                        <div className="px-3 py-2 text-gray-400 text-sm">No asset tags found</div>
                      ) : (
                        (assetTagSearch.length > 0 ? filteredAssetTags : assetTagOptions).map(tag => (
                          <label key={tag} className="flex items-center px-3 py-2 cursor-pointer hover:bg-blue-50">
                            <input
                              type="checkbox"
                              checked={newRequest.assetTags.includes(tag)}
                              onChange={e => {
                                if (e.target.checked) {
                                  setNewRequest(prev => ({ ...prev, assetTags: [...prev.assetTags, tag] }));
                                } else {
                                  setNewRequest(prev => ({ ...prev, assetTags: prev.assetTags.filter(t => t !== tag) }));
                                }
                                setAssetTagDropdownOpen(false); // Hide dropdown after selection
                              }}
                              className="mr-2"
                            />
                            {tag}
                          </label>
                        ))
                      )}
                    </div>
                  )}
                </div>
              </div>

              {/* Category Dropdown - New */}
              <div className="space-y-2">
                <label className="text-sm font-medium">category</label>
                <Select 
                  value={newRequest.category} 
                  onValueChange={(value) => setNewRequest(prev => ({ ...prev, category: value }))}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select category" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="AV computers">AV Computers</SelectItem>
                    <SelectItem value="AV printers">AV Printers</SelectItem>
                    <SelectItem value="pollpads">Pollpads</SelectItem>
                    <SelectItem value="tabs">Tabs</SelectItem>
                    <SelectItem value="scanners">Scanners</SelectItem>
                    <SelectItem value="monitors">Monitors</SelectItem>
                    <SelectItem value="others">Other</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* Name Dropdown - New */}
              <div className="space-y-2">
                <label className="text-sm font-medium">name</label>
                <Select 
                  value={newRequest.name} 
                  onValueChange={(value) => setNewRequest(prev => ({ ...prev, name: value }))}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select name" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="HP_COMPUTER">HP Computer</SelectItem>
                    <SelectItem value="DELL_COMPUTER">Dell Computer</SelectItem>
                    <SelectItem value="LENOVO_COMPUTER">Lenovo Computer</SelectItem>
                    <SelectItem value="HP_PRINTER">HP Printer</SelectItem>
                    <SelectItem value="CANON_PRINTER">Canon Printer</SelectItem>
                    <SelectItem value="EPSON_PRINTER">Epson Printer</SelectItem>
                    <SelectItem value="SAMSUNG_TAB">Samsung Tab</SelectItem>
                    <SelectItem value="IPAD">iPad</SelectItem>
                    <SelectItem value="ANDROID_TAB">Android Tab</SelectItem>
                    <SelectItem value="POLLPAD_DEVICE">Pollpad Device</SelectItem>
                    <SelectItem value="SCANNER_DEVICE">Scanner Device</SelectItem>
                    <SelectItem value="PROJECTOR">Projector</SelectItem>
                    <SelectItem value="MONITOR">Monitor</SelectItem>
                    <SelectItem value="OTHER">Other</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* Request Type Dropdown */}
              <div className="space-y-2">
                <label className="text-sm font-medium">request_type</label>
                <Select 
                  value={newRequest.requestType} 
                  onValueChange={(value) => setNewRequest(prev => ({ ...prev, requestType: value as 'preventive' | 'corrective' | 'emergency' }))}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="preventive">Preventive</SelectItem>
                    <SelectItem value="corrective">Corrective</SelectItem>
                    <SelectItem value="emergency">Emergency</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* Priority Dropdown */}
              <div className="space-y-2">
                <label className="text-sm font-medium">priority</label>
                <Select 
                  value={newRequest.priority} 
                  onValueChange={(value) => setNewRequest(prev => ({ ...prev, priority: value as 'low' | 'medium' | 'high' | 'critical' }))}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="low">Low</SelectItem>
                    <SelectItem value="medium">Medium</SelectItem>
                    <SelectItem value="high">High</SelectItem>
                    <SelectItem value="critical">Critical</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* Assigned Technician Dropdown */}
              <div className="space-y-2">
                <label className="text-sm font-medium">assigned_technician</label>
                <Select 
                  value={newRequest.assignedTechnician} 
                  onValueChange={(value) => setNewRequest(prev => ({ ...prev, assignedTechnician: value }))}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select technician" />
                  </SelectTrigger>
                  <SelectContent>
                    {technicians.map(technician => (
                      <SelectItem key={technician.id} value={technician.id}>
                        {technician.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>

            {/* Description */}
            <div className="space-y-2">
              <label className="text-sm font-medium">description</label>
              <Textarea
                placeholder="Describe the maintenance work needed..."
                value={newRequest.description}
                onChange={(e) => setNewRequest(prev => ({ ...prev, description: e.target.value }))}
                rows={3}
              />
            </div>

            {/* Bottom Row - Date and Frequency in first row, Hours and Cost in second row */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {/* Scheduled Date */}
              <div className="space-y-2">
                <label className="text-sm font-medium">scheduled_date</label>
                <Input
                  type="date"
                  value={newRequest.scheduledDate}
                  onChange={(e) => setNewRequest(prev => ({ ...prev, scheduledDate: e.target.value }))}
                  className="h-10"
                />
              </div>
              {/* Scheduled Frequency */}
              <div className="space-y-2">
                <label className="text-sm font-medium">scheduled_frequency</label>
                <Select
                  value={newRequest.scheduledFrequency}
                  onValueChange={(value) => setNewRequest(prev => ({ ...prev, scheduledFrequency: value }))}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select frequency" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="once">Once</SelectItem>
                    <SelectItem value="daily">Daily</SelectItem>
                    <SelectItem value="weekly">Weekly</SelectItem>
                    <SelectItem value="monthly">Monthly</SelectItem>
                    <SelectItem value="quarterly">Quarterly</SelectItem>
                    <SelectItem value="yearly">Yearly</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              {/* Estimated Hours */}
              <div className="space-y-2">
                <label className="text-sm font-medium">estimated_hours</label>
                <Input
                  type="number"
                  min="0.5"
                  step="0.5"
                  value={newRequest.estimatedHours}
                  onChange={(e) => setNewRequest(prev => ({ ...prev, estimatedHours: parseFloat(e.target.value) || 1 }))}
                  className="h-10"
                />
              </div>
            </div>
            {/* Estimated Cost */}
            <div className="space-y-2 mt-4">
              <label className="text-sm font-medium">estimated_cost ($)</label>
              <Input
                type="number"
                min="0"
                step="0.01"
                value={newRequest.estimatedCost}
                onChange={(e) => setNewRequest(prev => ({ ...prev, estimatedCost: parseFloat(e.target.value) || 0 }))}
                className="h-10"
              />
            </div>

            {/* Notes */}
            <div className="space-y-2">
              <label className="text-sm font-medium">notes</label>
              <Textarea
                placeholder="Additional notes or special instructions..."
                value={newRequest.notes}
                onChange={(e) => setNewRequest(prev => ({ ...prev, notes: e.target.value }))}
                rows={2}
              />
            </div>
          </div>

          {/* Action Buttons - Fixed at bottom */}
          <div className="flex justify-end space-x-2 pt-4 border-t flex-shrink-0">
            <Button variant="outline" onClick={() => setIsNewRequestModalOpen(false)}>
              Cancel
            </Button>
            <Button 
              onClick={handleCreateRequest}
              disabled={newRequest.assetTags.length === 0 || !newRequest.description || !newRequest.scheduledDate || actionLoading.create}
            >
              {actionLoading.create ? (
                <span className="flex items-center"><RefreshCw className="h-4 w-4 animate-spin mr-2" />Creating...</span>
              ) : (
                'Create Request'
              )}
            </Button>
          </div>
        </DialogContent>
      </Dialog>

      {/* Edit Modal */}
      <Dialog open={isEditModalOpen} onOpenChange={setIsEditModalOpen}>
        <DialogContent className="sm:max-w-[600px] max-h-[90vh] flex flex-col">
          <DialogHeader>
            <DialogTitle>Edit Maintenance Request</DialogTitle>
          </DialogHeader>
          {selectedRequest && (
            <div className="flex-1 overflow-y-auto px-1">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 py-4">
                {/* Asset Tag (read-only) */}
                <div className="space-y-2">
                  <label className="text-sm font-medium">asset_tag</label>
                  <Input value={editRequest.assetTag || ''} disabled />
                </div>
                {/* Category */}
                <div className="space-y-2">
                  <label className="text-sm font-medium">Category</label>
                  <Select
                    value={editRequest.category || ''}
                    onValueChange={value => setEditRequest(prev => ({ ...prev, category: value }))}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select category" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="AV computers">AV Computers</SelectItem>
                      <SelectItem value="AV printers">AV Printers</SelectItem>
                      <SelectItem value="pollpads">Pollpads</SelectItem>
                      <SelectItem value="tabs">Tabs</SelectItem>
                      <SelectItem value="scanners">Scanners</SelectItem>
                      <SelectItem value="monitors">Monitors</SelectItem>
                      <SelectItem value="others">Other</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                {/* Name */}
                <div className="space-y-2">
                  <label className="text-sm font-medium">Name</label>
                  <Input
                    value={editRequest.name || ''}
                    onChange={e => setEditRequest(prev => ({ ...prev, name: e.target.value }))}
                  />
                </div>
                {/* Status */}
                <div className="space-y-2">
                  <label className="text-sm font-medium">Status</label>
                  <Select
                    value={editRequest.status || ''}
                    onValueChange={value => setEditRequest(prev => ({ ...prev, status: value }))}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select status" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="REQUESTED">Requested</SelectItem>
                      <SelectItem value="SCHEDULED">Scheduled</SelectItem>
                      <SelectItem value="IN_PROGRESS">In Progress</SelectItem>
                      <SelectItem value="TESTING">Testing</SelectItem>
                      <SelectItem value="COMPLETED">Completed</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                {/* Priority */}
                <div className="space-y-2">
                  <label className="text-sm font-medium">Priority</label>
                  <Select
                    value={editRequest.priority || ''}
                    onValueChange={value => setEditRequest(prev => ({ ...prev, priority: value as 'low' | 'medium' | 'high' | 'critical' }))}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select priority" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="low">Low</SelectItem>
                      <SelectItem value="medium">Medium</SelectItem>
                      <SelectItem value="high">High</SelectItem>
                      <SelectItem value="critical">Critical</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                {/* Assigned Technician */}
                <div className="space-y-2">
                  <label className="text-sm font-medium">Assigned Technician</label>
                  <Select
                    value={editRequest.assignedTechnician || ''}
                    onValueChange={value => setEditRequest(prev => ({ ...prev, assignedTechnician: value }))}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select technician" />
                    </SelectTrigger>
                    <SelectContent>
                      {technicians.map(technician => (
                        <SelectItem key={technician.id} value={technician.id}>
                          {technician.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>
              {/* Description */}
              <div className="space-y-2">
                <label className="text-sm font-medium">Description</label>
                <Textarea
                  value={editRequest.description || ''}
                  onChange={e => setEditRequest(prev => ({ ...prev, description: e.target.value }))}
                  rows={3}
                />
              </div>
              {/* Scheduled Date */}
              <div className="space-y-2">
                <label className="text-sm font-medium">Scheduled Date</label>
                <Input
                  type="date"
                  value={editRequest.scheduledDate ? editRequest.scheduledDate.slice(0, 10) : ''}
                  onChange={e => setEditRequest(prev => ({ ...prev, scheduledDate: e.target.value }))}
                  className="h-10"
                />
              </div>
              {/* Scheduled Frequency */}
              <div className="space-y-2">
                <label className="text-sm font-medium">Scheduled Frequency</label>
                <select
                  value={editRequest.scheduledFrequency || ''}
                  onChange={e => setEditRequest(prev => ({ ...prev, scheduledFrequency: e.target.value }))}
                  className="h-10 w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="">Select frequency</option>
                  <option value="once">Once</option>
                  <option value="daily">Daily</option>
                  <option value="weekly">Weekly</option>
                  <option value="monthly">Monthly</option>
                  <option value="quarterly">Quarterly</option>
                  <option value="yearly">Yearly</option>
                </select>
              </div>
              {/* Estimated Hours */}
              <div className="space-y-2">
                <label className="text-sm font-medium">Estimated Hours</label>
                <Input
                  type="number"
                  min="0.5"
                  step="0.5"
                  value={editRequest.estimatedHours ?? ''}
                  onChange={e => setEditRequest(prev => ({ ...prev, estimatedHours: parseFloat(e.target.value) || 1 }))}
                  className="h-10"
                />
              </div>
              {/* Estimated Cost */}
              <div className="space-y-2">
                <label className="text-sm font-medium">Estimated Cost ($)</label>
                <Input
                  type="number"
                  min="0"
                  step="0.01"
                  value={editRequest.estimatedCost ?? ''}
                  onChange={e => setEditRequest(prev => ({ ...prev, estimatedCost: parseFloat(e.target.value) || 0 }))}
                  className="h-10"
                />
              </div>
              {/* Notes */}
              <div className="space-y-2">
                <label className="text-sm font-medium">Notes</label>
                <Textarea
                  value={editRequest.notes || ''}
                  onChange={e => setEditRequest(prev => ({ ...prev, notes: e.target.value }))}
                  rows={2}
                />
              </div>
            </div>
          )}
          {/* Action Buttons */}
          <div className="flex justify-end space-x-2 pt-4 border-t flex-shrink-0">
            <Button variant="outline" onClick={() => setIsEditModalOpen(false)}>
              Cancel
            </Button>
            <Button
              onClick={handleUpdateRequest}
              disabled={!editRequest.assetTag || !editRequest.description || !editRequest.scheduledDate || actionLoading.update}
            >
              {actionLoading.update ? (
                <span className="flex items-center"><RefreshCw className="h-4 w-4 animate-spin mr-2" />Saving...</span>
              ) : (
                'Save Changes'
              )}
            </Button>
          </div>
        </DialogContent>
      </Dialog>

      {/* View Maintenance Request Modal */}
      <Dialog open={isViewModalOpen} onOpenChange={setIsViewModalOpen}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>Maintenance Request Details</DialogTitle>
          </DialogHeader>
          {viewedRequest && (
            <div className="space-y-2">
              <div><strong>asset_tag:</strong> {viewedRequest.assetTag}</div>
              <div><strong>category:</strong> {viewedRequest.category || 'Unknown'}</div>
              <div><strong>name:</strong> {viewedRequest.name || 'Unknown'}</div>
              <div><strong>status:</strong> {viewedRequest.status}</div>
              <div><strong>description:</strong> {viewedRequest.description}</div>
              <div><strong>assigned_technician:</strong> {viewedRequest.assignedTechnician || 'Unassigned'}</div>
              <div><strong>scheduled_date:</strong> {viewedRequest.scheduledDate ? new Date(viewedRequest.scheduledDate).toLocaleDateString() : 'N/A'}</div>
              <div><strong>scheduled_frequency:</strong> {viewedRequest.scheduledFrequency || 'N/A'}</div>
              <div><strong>estimated_hours:</strong> {viewedRequest.estimatedHours}</div>
              <div><strong>estimated_cost:</strong> ${viewedRequest.estimatedCost}</div>
              <div><strong>notes:</strong> {viewedRequest.notes || 'None'}</div>
            </div>
          )}
        </DialogContent>
      </Dialog>

      {/* Delete Maintenance Request Modal */}
      <Dialog open={isDeleteModalOpen} onOpenChange={setIsDeleteModalOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Delete Maintenance Request</DialogTitle>
          </DialogHeader>
          <div className="flex justify-end space-x-2 pt-4 border-t flex-shrink-0">
            <Button variant="destructive" onClick={handleDeleteRequest} disabled={actionLoading.delete}>
              {actionLoading.delete ? (
                <span className="flex items-center"><RefreshCw className="h-4 w-4 animate-spin mr-2" />Deleting...</span>
              ) : (
                'Delete'
              )}
            </Button>
            <Button variant="outline" onClick={() => setIsDeleteModalOpen(false)}>
              Cancel
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};