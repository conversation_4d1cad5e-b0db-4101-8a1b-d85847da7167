import { AppLayout } from "@/components/layout/AppLayout";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Link } from "react-router-dom";
import {
  Users,
  History,
  Settings,
  Scan,
  Search,
  Download,
  Filter,
  Calendar,
  Shield,
  Activity,
  FileText,
  Eye,
  Clock,
  MapPin,
  User,
  Database,
  Loader2,
  AlertCircle
} from "lucide-react";
import { useState, useEffect } from "react";

interface AuditStats {
  totalEntries: number;
  todayActivity: number;
  activeUsers: number;
  securityAlerts: number;
  assetScans: number;
}

interface RecentActivity {
  id: string;
  type: string;
  action: string;
  user: string;
  timestamp: string;
  details: string;
  severity: 'info' | 'warning' | 'success' | 'error';
}

export default function AuditReportsPage() {
  const [auditStats, setAuditStats] = useState<AuditStats | null>(null);
  const [recentActivity, setRecentActivity] = useState<RecentActivity[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchAuditData();
  }, []);

  const fetchAuditData = async () => {
    try {
      setLoading(true);
      setError(null);

      // Fetch both stats and recent activity
      const [statsResponse, activityResponse] = await Promise.all([
        fetch('http://localhost:8000/api/reports/audit/stats', {
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('token')}`,
            'Content-Type': 'application/json',
          },
        }),
        fetch('http://localhost:8000/api/reports/audit/recent-activity?limit=5', {
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('token')}`,
            'Content-Type': 'application/json',
          },
        })
      ]);

      if (statsResponse.ok) {
        const statsResult = await statsResponse.json();
        if (statsResult.success) {
          setAuditStats(statsResult.data);
        }
      }

      if (activityResponse.ok) {
        const activityResult = await activityResponse.json();
        if (activityResult.success) {
          setRecentActivity(activityResult.data);
        }
      }

      if (!statsResponse.ok && !activityResponse.ok) {
        throw new Error('Failed to fetch audit data');
      }
    } catch (err) {
      console.error('Error fetching audit data:', err);
      setError(err instanceof Error ? err.message : 'Failed to load audit data');
    } finally {
      setLoading(false);
    }
  };
  const auditCategories = [
    {
      title: "User Activity Logs",
      description: "Track all actions performed by users within the system for transparency and accountability",
      icon: Users,
      path: "/reports-analytics/audit/user-activity",
      color: "blue"
    },
    {
      title: "Asset Movement History",
      description: "Complete movement record of each asset across departments, locations, or users",
      icon: History,
      path: "/reports-analytics/audit/asset-movement",
      color: "green"
    },
    {
      title: "System Access Reports",
      description: "Insights into who accessed the system, when, and from where",
      icon: Shield,
      path: "/reports-analytics/audit/system-access",
      color: "orange"
    },
    {
      title: "Change Management Logs",
      description: "Records all system configuration and asset data changes over time",
      icon: Settings,
      path: "/reports-analytics/audit/change-management",
      color: "purple"
    },
    {
      title: "Asset Scan Logs",
      description: "Physical audit support with barcode scanning logs and verification records",
      icon: Scan,
      path: "/reports-analytics/audit/asset-scan",
      color: "indigo"
    }
  ];

  // Recent activity data now comes from backend

  const getIconColor = (color: string) => {
    const colors = {
      blue: "text-blue-600",
      green: "text-green-600",
      orange: "text-orange-600",
      purple: "text-purple-600",
      indigo: "text-indigo-600"
    };
    return colors[color as keyof typeof colors] || "text-gray-600";
  };

  const getBgColor = (color: string) => {
    const colors = {
      blue: "bg-blue-50",
      green: "bg-green-50",
      orange: "bg-orange-50",
      purple: "bg-purple-50",
      indigo: "bg-indigo-50"
    };
    return colors[color as keyof typeof colors] || "bg-gray-50";
  };

  return (
    <AppLayout>
      <div className="space-y-6 animate-fade-in p-6">
        <div className="flex flex-col md:flex-row md:items-center justify-between gap-4">
          <div>
            <h1 className="text-2xl font-bold text-zenith-blue-dark mb-2">Audit Reports</h1>
            <p className="text-muted-foreground">
              Comprehensive audit trail with user activity, asset movements, and system changes
            </p>
          </div>
          <div className="flex gap-2">
            <Button variant="outline" className="flex items-center gap-2">
              <Filter className="h-4 w-4" />
              Advanced Filters
            </Button>
            <Button className="flex items-center gap-2">
              <Download className="h-4 w-4" />
              Export Audit Log
            </Button>
          </div>
        </div>

        {/* Quick Stats */}
        {loading ? (
          <div className="flex items-center justify-center h-32">
            <Loader2 className="h-8 w-8 animate-spin" />
            <span className="ml-2">Loading audit statistics...</span>
          </div>
        ) : error ? (
          <div className="flex items-center justify-center h-32">
            <div className="text-center">
              <AlertCircle className="h-8 w-8 text-red-500 mx-auto mb-4" />
              <p className="text-red-600 mb-4">{error}</p>
              <Button onClick={fetchAuditData}>Retry</Button>
            </div>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Entries</CardTitle>
                <Database className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{auditStats?.totalEntries || 0}</div>
                <p className="text-xs text-muted-foreground">Last 30 days</p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Today's Activity</CardTitle>
                <Activity className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{auditStats?.todayActivity || 0}</div>
                <p className="text-xs text-muted-foreground">Activities today</p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Active Users</CardTitle>
                <User className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{auditStats?.activeUsers || 0}</div>
                <p className="text-xs text-muted-foreground">Currently online</p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Security Alerts</CardTitle>
                <Shield className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{auditStats?.securityAlerts || 0}</div>
                <p className="text-xs text-muted-foreground">Requires attention</p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Asset Scans</CardTitle>
                <Scan className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{auditStats?.assetScans || 0}</div>
                <p className="text-xs text-muted-foreground">This week</p>
              </CardContent>
            </Card>
          </div>
        )}

        <Tabs defaultValue="overview" className="w-full">
          <TabsList>
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="categories">Audit Categories</TabsTrigger>
            <TabsTrigger value="search">Advanced Search</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-6">
            {/* Recent Activity */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Clock className="h-5 w-5" />
                  Recent Audit Activity
                </CardTitle>
                <CardDescription>Latest system activities and changes</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {recentActivity.length > 0 ? recentActivity.map((activity) => (
                    <div key={activity.id} className={`flex items-center justify-between p-3 rounded-lg ${
                      activity.severity === 'warning' ? 'bg-yellow-50' :
                      activity.severity === 'success' ? 'bg-green-50' : 'bg-blue-50'
                    }`}>
                      <div className="flex items-center gap-3">
                        <div className={`p-2 rounded-full ${
                          activity.severity === 'warning' ? 'bg-yellow-100' :
                          activity.severity === 'success' ? 'bg-green-100' : 'bg-blue-100'
                        }`}>
                          {activity.type === 'user_activity' && <Users className="h-4 w-4 text-blue-600" />}
                          {activity.type === 'asset_movement' && <History className="h-4 w-4 text-green-600" />}
                          {activity.type === 'system_access' && <Shield className="h-4 w-4 text-orange-600" />}
                          {activity.type === 'change_management' && <Settings className="h-4 w-4 text-purple-600" />}
                          {activity.type === 'asset_scan' && <Scan className="h-4 w-4 text-indigo-600" />}
                        </div>
                        <div>
                          <p className="text-sm font-medium">{activity.action}</p>
                          <p className="text-xs text-muted-foreground">{activity.details}</p>
                          <p className="text-xs text-muted-foreground">{activity.user} • {activity.timestamp}</p>
                        </div>
                      </div>
                      <Button size="sm" variant="outline">
                        <Eye className="h-4 w-4" />
                      </Button>
                    </div>
                  )) : (
                    <div className="text-center py-8">
                      <Clock className="h-8 w-8 text-muted-foreground mx-auto mb-4" />
                      <p className="text-muted-foreground">No recent activity found</p>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="categories" className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {auditCategories.map((category) => {
                const IconComponent = category.icon;
                return (
                  <Link key={category.title} to={category.path}>
                    <Card className={`h-full cursor-pointer hover:shadow-md transition-all ${getBgColor(category.color)}`}>
                      <CardHeader className="pb-4">
                        <div className="flex items-center gap-3">
                          <div className={`p-3 rounded-lg bg-white shadow-sm`}>
                            <IconComponent className={`h-6 w-6 ${getIconColor(category.color)}`} />
                          </div>
                          <div>
                            <CardTitle className="text-lg">{category.title}</CardTitle>
                            <CardDescription className="text-sm">{category.description}</CardDescription>
                          </div>
                        </div>
                      </CardHeader>
                      <CardContent>
                        <p className="text-sm text-muted-foreground">
                          Click to view detailed {category.title.toLowerCase()} and analytics
                        </p>
                      </CardContent>
                    </Card>
                  </Link>
                );
              })}
            </div>
          </TabsContent>

          <TabsContent value="search" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Search className="h-5 w-5" />
                  Advanced Audit Search
                </CardTitle>
                <CardDescription>Search and filter audit logs with advanced criteria</CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div>
                    <Label>Audit Type</Label>
                    <Select>
                      <SelectTrigger>
                        <SelectValue placeholder="Select type" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">All Types</SelectItem>
                        <SelectItem value="user_activity">User Activity</SelectItem>
                        <SelectItem value="asset_movement">Asset Movement</SelectItem>
                        <SelectItem value="system_access">System Access</SelectItem>
                        <SelectItem value="change_management">Change Management</SelectItem>
                        <SelectItem value="asset_scan">Asset Scan</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <Label>Date Range</Label>
                    <Select>
                      <SelectTrigger>
                        <SelectValue placeholder="Select range" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="today">Today</SelectItem>
                        <SelectItem value="week">Last 7 days</SelectItem>
                        <SelectItem value="month">Last 30 days</SelectItem>
                        <SelectItem value="quarter">Last 3 months</SelectItem>
                        <SelectItem value="custom">Custom Range</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <Label>Severity</Label>
                    <Select>
                      <SelectTrigger>
                        <SelectValue placeholder="Select severity" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">All Levels</SelectItem>
                        <SelectItem value="info">Info</SelectItem>
                        <SelectItem value="warning">Warning</SelectItem>
                        <SelectItem value="error">Error</SelectItem>
                        <SelectItem value="critical">Critical</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label>User/Email</Label>
                    <Input placeholder="Search by user email or name" />
                  </div>
                  <div>
                    <Label>IP Address</Label>
                    <Input placeholder="Search by IP address" />
                  </div>
                </div>

                <div>
                  <Label>Keywords</Label>
                  <Input placeholder="Search in activity descriptions and details" />
                </div>

                <div className="flex gap-2 pt-4 border-t">
                  <Button className="flex items-center gap-2">
                    <Search className="h-4 w-4" />
                    Search Audit Logs
                  </Button>
                  <Button variant="outline" className="flex items-center gap-2">
                    <Download className="h-4 w-4" />
                    Export Results
                  </Button>
                  <Button variant="outline">Clear Filters</Button>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </AppLayout>
  );
}
