import React, { useState } from 'react';
import { useAppContext } from '@/context/AppContext';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Ta<PERSON>, <PERSON><PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Plus, Workflow, Calendar, BarChart3, CalendarClock, Clock, Wrench as WrenchIcon, TestTube, CheckCircle2 } from 'lucide-react';
import { ServiceProviderModal } from './ServiceProviderModal';
import { MaintenanceWorkflowModal } from './MaintenanceWorkflowModal';
import { CompleteMaintenanceModal } from './CompleteMaintenanceModal';
import { GroupMaintenanceRequestModal } from './GroupMaintenanceRequestModal';

const workflowStages = [
  { 
    id: 'REQUESTED', 
    label: 'Requested', 
    count: 0, 
    icon: CalendarClock,
    bgColor: 'bg-gray-100'
  },
  { 
    id: 'SCHEDULED', 
    label: 'Scheduled', 
    count: 0,
    icon: Clock,
    bgColor: 'bg-blue-100'
  },
  { 
    id: 'IN_PROGRESS', 
    label: 'In Progress', 
    count: 0,
    icon: WrenchIcon,
    bgColor: 'bg-orange-100'
  },
  { 
    id: 'TESTING', 
    label: 'Testing', 
    count: 0,
    icon: TestTube,
    bgColor: 'bg-purple-100'
  },
  { 
    id: 'COMPLETED', 
    label: 'Completed', 
    count: 0,
    icon: CheckCircle2,
    bgColor: 'bg-green-100'
  }
];

export const MaintenanceTab: React.FC = () => {
  const { state, dispatch } = useAppContext();
  const [searchTerm, setSearchTerm] = useState('');
  const [isNewSingleModalOpen, setIsNewSingleModalOpen] = useState(false);
  const [isGroupModalOpen, setIsGroupModalOpen] = useState(false);
  const [isServiceProviderModalOpen, setIsServiceProviderModalOpen] = useState(false);
  const [isCompleteModalOpen, setIsCompleteModalOpen] = useState(false);
  const [selectedAssetForCompletion, setSelectedAssetForCompletion] = useState<string | null>(null);
  const [activeView, setActiveView] = useState('workflow');

  const handleAddServiceProvider = () => {
    setIsServiceProviderModalOpen(true);
  };

  const handleCompleteMaintenanceSuccess = () => {
    // Refresh data or show success message
    setIsCompleteModalOpen(false);
    setSelectedAssetForCompletion(null);
  };

  const handleNewMaintenanceSuccess = () => {
    // Refresh data or show success message
    setIsNewSingleModalOpen(false);
  };

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Maintenance Management</h1>
          <p className="text-gray-600 mt-2">Track and manage equipment maintenance requests</p>
        </div>
        <div className="flex space-x-3">
          <Button 
            onClick={() => setIsNewSingleModalOpen(true)}
            className="flex items-center space-x-2"
          >
            <Plus size={16} />
            <span>New Maintenance Request</span>
          </Button>
          <Button 
            onClick={() => setIsGroupModalOpen(true)}
            variant="outline"
            className="flex items-center space-x-2"
          >
            <Plus size={16} />
            <span>Group Request</span>
          </Button>
          <Button 
            onClick={handleAddServiceProvider}
            variant="outline"
            className="flex items-center space-x-2"
          >
            <Plus size={16} />
            <span>Service Provider</span>
          </Button>
        </div>
      </div>

      {/* Main Tabs */}
      <Tabs value={activeView} onValueChange={setActiveView} className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="workflow" className="flex items-center space-x-2">
            <Workflow size={16} />
            <span>Workflow Board</span>
          </TabsTrigger>
          <TabsTrigger value="calendar" className="flex items-center space-x-2">
            <Calendar size={16} />
            <span>Calendar View</span>
          </TabsTrigger>
          <TabsTrigger value="analytics" className="flex items-center space-x-2">
            <BarChart3 size={16} />
            <span>Analytics</span>
          </TabsTrigger>
        </TabsList>

        {/* Workflow Board Tab */}
        <TabsContent value="workflow" className="space-y-6">
          <div className="grid grid-cols-5 gap-4">
            {workflowStages.map((stage) => {
              const StageIcon = stage.icon;
              return (
                <Card key={stage.id}>
                  <CardContent className="p-6">
                    <div className="flex flex-col items-center">
                      <div className={`p-3 rounded-full ${stage.bgColor} mb-4`}>
                        <StageIcon className="h-5 w-5" />
                      </div>
                      <h3 className="font-medium">{stage.label}</h3>
                      <span className="text-sm text-gray-500 mt-1">{stage.count}</span>
                      <div className="mt-8 text-sm text-gray-500 text-center">
                        {`No ${stage.label.toLowerCase()} requests`}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              );
            })}
          </div>
        </TabsContent>

        {/* Calendar View Tab */}
        <TabsContent value="calendar" className="space-y-6">
          <div className="text-center py-8 text-gray-500">
            Calendar view coming soon...
          </div>
        </TabsContent>

        {/* Analytics Tab */}
        <TabsContent value="analytics" className="space-y-6">
          <div className="text-center py-8 text-gray-500">
            Analytics view coming soon...
          </div>
        </TabsContent>
      </Tabs>

      {/* Modals */}
      <MaintenanceWorkflowModal
        isOpen={isNewSingleModalOpen}
        onClose={() => setIsNewSingleModalOpen(false)}
        onSuccess={handleNewMaintenanceSuccess}
      />

      <GroupMaintenanceRequestModal
        isOpen={isGroupModalOpen}
        onClose={() => setIsGroupModalOpen(false)}
      />

      <CompleteMaintenanceModal
        isOpen={isCompleteModalOpen}
        onClose={() => setIsCompleteModalOpen(false)}
        assetTag={selectedAssetForCompletion || ''}
        onSuccess={handleCompleteMaintenanceSuccess}
      />

      <ServiceProviderModal
        open={isServiceProviderModalOpen}
        onOpenChange={setIsServiceProviderModalOpen}
      />
    </div>
  );
};
