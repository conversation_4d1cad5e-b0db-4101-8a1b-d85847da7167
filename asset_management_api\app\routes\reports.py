# app/routes/reports.py
from fastapi import APIRouter, HTTPException, Depends, status, Query
from sqlalchemy.orm import Session
from sqlalchemy import func, and_, or_, desc, text
from typing import List, Optional, Dict, Any
from datetime import datetime, timedelta
import logging
import json

from app.config.database import get_db
from app.models.user import User
from app.models.assets import Asset
from app.models.masters_consumables import MastersConsumable, ConsumableStock
from app.models.asset_transfers import AssetTransfer
from app.models.damage_reports import DamageReport
from app.utils.auth import get_current_user

# Import audit models (using raw SQL queries to avoid import issues)
# from app.models.audit_logs import UserActivityLog, SystemAccessLog, AssetScanLog, ChangeManagementLog, ReportGenerationLog
# from app.models.scheduled_reports import ScheduledReport, CustomReport

router = APIRouter()
logger = logging.getLogger(__name__)

@router.get("/analytics/dashboard")
async def get_analytics_dashboard(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    Get analytics dashboard data for reports.
    Frontend expects this for reports & analytics page.
    """
    try:
        # Asset statistics
        total_assets = db.query(Asset).count()
        operational_assets = db.query(Asset).filter(Asset.status == 'operational').count()
        maintenance_assets = db.query(Asset).filter(Asset.status == 'maintenance').count()
        repair_assets = db.query(Asset).filter(Asset.status == 'repair').count()
        
        # Assets by type
        asset_types = db.query(
            Asset.category,
            func.count(Asset.id).label('count')
        ).group_by(Asset.category).all()
        
        # Recent asset transfers
        recent_transfers = db.query(AssetTransfer).order_by(
            desc(AssetTransfer.created_at)
        ).limit(10).all()
        
        # Damage reports this month
        thirty_days_ago = datetime.utcnow() - timedelta(days=30)
        damage_reports_count = db.query(DamageReport).filter(
            DamageReport.created_at >= thirty_days_ago
        ).count()
        
        # User activity statistics
        total_users = db.query(User).count()
        active_users = db.query(User).filter(User.status == True).count()
        
        # Consumables low stock alerts
        low_stock_consumables = db.query(ConsumableStock).filter(
            ConsumableStock.current_stock <= ConsumableStock.min_stock_level
        ).count()
        
        return {
            "success": True,
            "data": {
                "assets": {
                    "total": total_assets,
                    "operational": operational_assets,
                    "maintenance": maintenance_assets,
                    "repair": repair_assets,
                    "operational_percentage": round((operational_assets / total_assets * 100) if total_assets > 0 else 0, 1)
                },
                "asset_types": [{"type": t[0], "count": t[1]} for t in asset_types],
                "recent_transfers": len(recent_transfers),
                "damage_reports_month": damage_reports_count,
                "users": {
                    "total": total_users,
                    "active": active_users,
                    "inactive": total_users - active_users
                },
                "low_stock_alerts": low_stock_consumables,
                "last_updated": datetime.utcnow().isoformat()
            }
        }
        
    except Exception as e:
        logger.error(f"Error getting analytics dashboard: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get analytics data: {str(e)}"
        )

@router.get("/custom/generate")
async def generate_custom_report(
    report_type: str = Query(..., description="Type of report to generate"),
    start_date: Optional[str] = Query(None, description="Start date for report"),
    end_date: Optional[str] = Query(None, description="End date for report"),
    filters: Optional[str] = Query(None, description="Additional filters as JSON"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    Generate custom reports based on parameters.
    Frontend expects this for custom reports functionality.
    """
    try:
        # Parse date filters
        start_dt = None
        end_dt = None
        if start_date:
            start_dt = datetime.fromisoformat(start_date.replace('Z', '+00:00'))
        if end_date:
            end_dt = datetime.fromisoformat(end_date.replace('Z', '+00:00'))
        
        report_data = {}
        
        if report_type == "asset_status":
            # Asset status report
            query = db.query(Asset)
            if start_dt:
                query = query.filter(Asset.created_at >= start_dt)
            if end_dt:
                query = query.filter(Asset.created_at <= end_dt)
            
            assets = query.all()
            report_data = {
                "report_type": "Asset Status Report",
                "total_assets": len(assets),
                "assets_by_status": {},
                "assets_by_type": {},
                "generated_at": datetime.utcnow().isoformat(),
                "date_range": {
                    "start": start_date,
                    "end": end_date
                }
            }
            
            # Group by status
            status_counts = {}
            type_counts = {}
            for asset in assets:
                status = asset.status or 'unknown'
                asset_type = asset.category or 'unknown'
                
                status_counts[status] = status_counts.get(status, 0) + 1
                type_counts[asset_type] = type_counts.get(asset_type, 0) + 1
            
            report_data["assets_by_status"] = status_counts
            report_data["assets_by_type"] = type_counts
            
        elif report_type == "user_activity":
            # User activity report
            query = db.query(User)
            if current_user.access_level == 'county' and current_user.county:
                query = query.filter(User.county == current_user.county)
            
            users = query.all()
            report_data = {
                "report_type": "User Activity Report",
                "total_users": len(users),
                "active_users": len([u for u in users if u.status]),
                "users_by_role": {},
                "users_by_access_level": {},
                "generated_at": datetime.utcnow().isoformat()
            }
            
            # Group by role and access level
            role_counts = {}
            access_counts = {}
            for user in users:
                role = user.role or 'unknown'
                access = user.access_level or 'unknown'
                
                role_counts[role] = role_counts.get(role, 0) + 1
                access_counts[access] = access_counts.get(access, 0) + 1
            
            report_data["users_by_role"] = role_counts
            report_data["users_by_access_level"] = access_counts
            
        elif report_type == "maintenance_schedule":
            # Maintenance schedule report
            # For now, return mock data since maintenance scheduling isn't fully implemented
            report_data = {
                "report_type": "Maintenance Schedule Report",
                "upcoming_maintenance": [],
                "overdue_maintenance": [],
                "completed_this_month": 0,
                "generated_at": datetime.utcnow().isoformat()
            }
            
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Unsupported report type: {report_type}"
            )
        
        return {
            "success": True,
            "data": report_data
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error generating custom report: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to generate report: {str(e)}"
        )

@router.get("/audit/user-activity")
async def get_user_activity_audit(
    start_date: Optional[str] = Query(None),
    end_date: Optional[str] = Query(None),
    user_id: Optional[str] = Query(None),
    activity_type: Optional[str] = Query(None),
    module: Optional[str] = Query(None),
    limit: int = Query(100, le=1000),
    offset: int = Query(0, ge=0),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    Get user activity audit logs from database.
    Frontend expects this for audit reports.
    """
    try:
        # Build query with filters
        query = """
        SELECT
            id, user_id, user_name, user_email, activity_type, action, module,
            resource_type, resource_id, resource_name, ip_address, user_agent,
            status, details, created_at
        FROM user_activity_logs
        WHERE 1=1
        """
        params = {}

        if start_date:
            query += " AND created_at >= :start_date"
            params['start_date'] = start_date

        if end_date:
            query += " AND created_at <= :end_date"
            params['end_date'] = end_date

        if user_id:
            query += " AND user_id = :user_id"
            params['user_id'] = user_id

        if activity_type:
            query += " AND activity_type = :activity_type"
            params['activity_type'] = activity_type

        if module:
            query += " AND module = :module"
            params['module'] = module

        query += " ORDER BY created_at DESC LIMIT :limit OFFSET :offset"
        params['limit'] = limit
        params['offset'] = offset

        result = db.execute(text(query), params)
        activities = []

        for row in result:
            activities.append({
                "id": str(row[0]),
                "userId": row[1],
                "userName": row[2],
                "userEmail": row[3],
                "activityType": row[4],
                "action": row[5],
                "module": row[6],
                "resourceType": row[7],
                "resourceId": row[8],
                "resourceName": row[9],
                "ipAddress": row[10],
                "userAgent": row[11],
                "status": row[12],
                "details": row[13],
                "timestamp": row[14].isoformat() if row[14] else None
            })

        # Get total count for pagination
        count_query = """
        SELECT COUNT(*) FROM user_activity_logs WHERE 1=1
        """
        count_params = {}

        if start_date:
            count_query += " AND created_at >= :start_date"
            count_params['start_date'] = start_date

        if end_date:
            count_query += " AND created_at <= :end_date"
            count_params['end_date'] = end_date

        if user_id:
            count_query += " AND user_id = :user_id"
            count_params['user_id'] = user_id

        if activity_type:
            count_query += " AND activity_type = :activity_type"
            count_params['activity_type'] = activity_type

        if module:
            count_query += " AND module = :module"
            count_params['module'] = module

        total_count = db.execute(text(count_query), count_params).scalar()

        return {
            "success": True,
            "data": {
                "activities": activities,
                "pagination": {
                    "total": total_count,
                    "limit": limit,
                    "offset": offset,
                    "hasMore": offset + limit < total_count
                }
            }
        }
        
    except Exception as e:
        logger.error(f"Error getting user activity audit: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get audit data: {str(e)}"
        )

@router.get("/audit/asset-movement")
async def get_asset_movement_audit(
    start_date: Optional[str] = Query(None),
    end_date: Optional[str] = Query(None),
    asset_id: Optional[str] = Query(None),
    status: Optional[str] = Query(None),
    limit: int = Query(100, le=1000),
    offset: int = Query(0, ge=0),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    Get asset movement audit logs from asset transfers.
    Frontend expects this for audit reports.
    """
    try:
        # Query asset transfers as movement audit
        query = db.query(AssetTransfer)

        if start_date:
            try:
                start_dt = datetime.fromisoformat(start_date.replace('Z', '+00:00'))
                query = query.filter(AssetTransfer.created_at >= start_dt)
            except ValueError:
                pass

        if end_date:
            try:
                end_dt = datetime.fromisoformat(end_date.replace('Z', '+00:00'))
                query = query.filter(AssetTransfer.created_at <= end_dt)
            except ValueError:
                pass
        
        if asset_id:
            query = query.filter(AssetTransfer.asset_id == asset_id)
        
        transfers = query.order_by(desc(AssetTransfer.created_at)).limit(100).all()
        
        movements = []
        for transfer in transfers:
            movements.append({
                "id": str(transfer.id),
                "asset_id": transfer.asset_id,
                "from_location": transfer.from_location,
                "to_location": transfer.to_location,
                "transfer_date": transfer.transfer_date.isoformat() if transfer.transfer_date else None,
                "status": transfer.status,
                "notes": transfer.notes,
                "created_at": transfer.created_at.isoformat() if transfer.created_at else None
            })
        
        return {
            "success": True,
            "data": {
                "movements": movements,
                "total": len(movements)
            }
        }
        
    except Exception as e:
        logger.error(f"Error getting asset movement audit: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get asset movement data: {str(e)}"
        )

@router.get("/audit/system-access")
async def get_system_access_audit(
    start_date: Optional[str] = Query(None),
    end_date: Optional[str] = Query(None),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    Get system access audit logs.
    Frontend expects this for audit reports.
    """
    try:
        # Return mock system access data
        # In a full implementation, you would query login/logout logs
        
        access_logs = [
            {
                "id": "1",
                "user_id": current_user.id,
                "user_name": f"{current_user.first_name} {current_user.last_name}",
                "event_type": "LOGIN",
                "timestamp": datetime.utcnow().isoformat(),
                "ip_address": "*************",
                "success": True
            },
            {
                "id": "2",
                "user_id": current_user.id,
                "user_name": f"{current_user.first_name} {current_user.last_name}",
                "event_type": "LOGOUT",
                "timestamp": (datetime.utcnow() - timedelta(hours=1)).isoformat(),
                "ip_address": "*************",
                "success": True
            }
        ]
        
        return {
            "success": True,
            "data": {
                "access_logs": access_logs,
                "total": len(access_logs)
            }
        }
        
    except Exception as e:
        logger.error(f"Error getting system access audit: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get system access data: {str(e)}"
        )

@router.get("/scheduled")
async def get_scheduled_reports(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    Get scheduled reports.
    Frontend expects this for scheduled reports functionality.
    """
    try:
        # Return mock scheduled reports data
        # In a full implementation, you would query a scheduled_reports table
        
        scheduled_reports = [
            {
                "id": "1",
                "name": "Weekly Asset Status Report",
                "description": "Automated weekly report on asset status",
                "schedule": "Weekly",
                "next_run": (datetime.utcnow() + timedelta(days=1)).isoformat(),
                "last_run": (datetime.utcnow() - timedelta(days=6)).isoformat(),
                "status": "Active",
                "created_by": current_user.email,
                "recipients": [current_user.email]
            },
            {
                "id": "2",
                "name": "Monthly User Activity Report",
                "description": "Monthly summary of user activities",
                "schedule": "Monthly",
                "next_run": (datetime.utcnow() + timedelta(days=25)).isoformat(),
                "last_run": (datetime.utcnow() - timedelta(days=5)).isoformat(),
                "status": "Active",
                "created_by": current_user.email,
                "recipients": [current_user.email]
            }
        ]
        
        return {
            "success": True,
            "data": {
                "scheduled_reports": scheduled_reports,
                "total": len(scheduled_reports)
            }
        }
        
    except Exception as e:
        logger.error(f"Error getting scheduled reports: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get scheduled reports: {str(e)}"
        )

@router.post("/export")
async def export_report(
    export_data: dict,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    Export report data in various formats.
    Frontend expects this for report export functionality.
    """
    try:
        report_type = export_data.get("type", "")
        format_type = export_data.get("format", "json")
        data = export_data.get("data", {})
        
        # In a full implementation, you would generate actual files (PDF, Excel, CSV)
        # For now, return success with download info
        
        export_info = {
            "export_id": f"export_{datetime.utcnow().strftime('%Y%m%d_%H%M%S')}",
            "filename": f"{report_type}_{datetime.utcnow().strftime('%Y%m%d')}.{format_type}",
            "format": format_type,
            "status": "completed",
            "download_url": f"/api/reports/download/export_{datetime.utcnow().strftime('%Y%m%d_%H%M%S')}",
            "generated_at": datetime.utcnow().isoformat(),
            "expires_at": (datetime.utcnow() + timedelta(days=7)).isoformat()
        }
        
        return {
            "success": True,
            "message": "Report exported successfully",
            "data": export_info
        }
        
    except Exception as e:
        logger.error(f"Error exporting report: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to export report: {str(e)}"
        )

# Audit stats endpoint
@router.get("/audit/stats")
async def get_audit_stats(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    Get audit statistics for the dashboard.
    """
    try:
        # Get total audit entries (last 30 days)
        thirty_days_ago = datetime.utcnow() - timedelta(days=30)

        # Count user activities
        user_activities_count = db.execute(text("""
            SELECT COUNT(*) FROM user_activity_logs
            WHERE created_at >= :start_date
        """), {"start_date": thirty_days_ago}).scalar() or 0

        # Count asset movements
        asset_movements_count = db.execute(text("""
            SELECT COUNT(*) FROM asset_transfers
            WHERE created_at >= :start_date
        """), {"start_date": thirty_days_ago}).scalar() or 0

        # Count system access logs
        system_access_count = db.execute(text("""
            SELECT COUNT(*) FROM system_access_logs
            WHERE created_at >= :start_date
        """), {"start_date": thirty_days_ago}).scalar() or 0

        total_entries = user_activities_count + asset_movements_count + system_access_count

        # Today's activity
        today_start = datetime.utcnow().replace(hour=0, minute=0, second=0, microsecond=0)
        today_activities = db.execute(text("""
            SELECT COUNT(*) FROM user_activity_logs
            WHERE created_at >= :today_start
        """), {"today_start": today_start}).scalar() or 0

        # Active users (users who logged in today)
        active_users = db.execute(text("""
            SELECT COUNT(DISTINCT user_id) FROM user_activity_logs
            WHERE created_at >= :today_start AND action LIKE '%login%'
        """), {"today_start": today_start}).scalar() or 0

        # Security alerts (failed login attempts today)
        security_alerts = db.execute(text("""
            SELECT COUNT(*) FROM user_activity_logs
            WHERE created_at >= :today_start AND status = 'failed' AND action LIKE '%login%'
        """), {"today_start": today_start}).scalar() or 0

        # Asset scans this week
        week_start = datetime.utcnow() - timedelta(days=7)
        asset_scans = db.execute(text("""
            SELECT COUNT(*) FROM asset_scan_logs
            WHERE created_at >= :week_start
        """), {"week_start": week_start}).scalar() or 0

        return {
            "success": True,
            "data": {
                "totalEntries": total_entries,
                "todayActivity": today_activities,
                "activeUsers": active_users,
                "securityAlerts": security_alerts,
                "assetScans": asset_scans
            }
        }

    except Exception as e:
        logger.error(f"Error getting audit stats: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get audit stats: {str(e)}"
        )

# Recent audit activity endpoint
@router.get("/audit/recent-activity")
async def get_recent_audit_activity(
    limit: int = Query(5, le=50),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    Get recent audit activity across all audit types.
    """
    try:
        # Get recent user activities
        recent_activities = db.execute(text("""
            SELECT
                id,
                'user_activity' as type,
                action,
                user_name as user,
                details,
                status as severity,
                created_at as timestamp
            FROM user_activity_logs
            ORDER BY created_at DESC
            LIMIT :limit
        """), {"limit": limit}).fetchall()

        activities = []
        for activity in recent_activities:
            activities.append({
                "id": str(activity.id),
                "type": activity.type,
                "action": activity.action,
                "user": activity.user,
                "timestamp": activity.timestamp.strftime("%Y-%m-%d %H:%M:%S") if activity.timestamp else "",
                "details": activity.details or "",
                "severity": activity.severity or "info"
            })

        return {
            "success": True,
            "data": activities
        }

    except Exception as e:
        logger.error(f"Error getting recent audit activity: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get recent audit activity: {str(e)}"
        )

@router.get("/audit/asset-scan")
async def get_asset_scan_audit(
    start_date: Optional[str] = Query(None),
    end_date: Optional[str] = Query(None),
    scan_type: Optional[str] = Query(None),
    status: Optional[str] = Query(None),
    asset_tag: Optional[str] = Query(None),
    limit: int = Query(100, le=1000),
    offset: int = Query(0, ge=0),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    Get asset scan audit logs from database.
    """
    try:
        # Build query with filters
        query = """
        SELECT
            id, asset_id, asset_tag, asset_name, asset_type, scan_type,
            location_name, scanned_by_name, status, is_valid_asset,
            validation_errors, notes, scanned_at, created_at
        FROM asset_scan_logs
        WHERE 1=1
        """
        params = {}

        if start_date:
            query += " AND created_at >= :start_date"
            params['start_date'] = start_date

        if end_date:
            query += " AND created_at <= :end_date"
            params['end_date'] = end_date

        if scan_type:
            query += " AND scan_type = :scan_type"
            params['scan_type'] = scan_type

        if status:
            query += " AND status = :status"
            params['status'] = status

        if asset_tag:
            query += " AND asset_tag LIKE :asset_tag"
            params['asset_tag'] = f"%{asset_tag}%"

        query += " ORDER BY created_at DESC LIMIT :limit OFFSET :offset"
        params['limit'] = limit
        params['offset'] = offset

        result = db.execute(text(query), params)
        scan_logs = []

        for row in result:
            scan_logs.append({
                "id": str(row[0]),
                "assetId": str(row[1]) if row[1] else None,
                "assetTag": row[2],
                "assetName": row[3],
                "assetType": row[4],
                "scanType": row[5],
                "location": row[6],
                "scannedBy": row[7],
                "status": row[8],
                "isValidAsset": row[9],
                "validationErrors": row[10],
                "notes": row[11],
                "scannedAt": row[12].isoformat() if row[12] else None,
                "timestamp": row[13].isoformat() if row[13] else None
            })

        # Get total count
        count_query = "SELECT COUNT(*) FROM asset_scan_logs WHERE 1=1"
        count_params = {}

        if start_date:
            count_query += " AND created_at >= :start_date"
            count_params['start_date'] = start_date

        if end_date:
            count_query += " AND created_at <= :end_date"
            count_params['end_date'] = end_date

        if scan_type:
            count_query += " AND scan_type = :scan_type"
            count_params['scan_type'] = scan_type

        if status:
            count_query += " AND status = :status"
            count_params['status'] = status

        if asset_tag:
            count_query += " AND asset_tag LIKE :asset_tag"
            count_params['asset_tag'] = f"%{asset_tag}%"

        total_count = db.execute(text(count_query), count_params).scalar()

        return {
            "success": True,
            "data": {
                "scanLogs": scan_logs,
                "pagination": {
                    "total": total_count,
                    "limit": limit,
                    "offset": offset,
                    "hasMore": offset + limit < total_count
                }
            }
        }

    except Exception as e:
        logger.error(f"Error getting asset scan audit: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get asset scan data: {str(e)}"
        )

@router.get("/scheduled")
async def get_scheduled_reports(
    status: Optional[str] = Query(None),
    limit: int = Query(100, le=1000),
    offset: int = Query(0, ge=0),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    Get scheduled reports from database.
    """
    try:
        # Build query with filters
        query = """
        SELECT
            id, name, description, report_type, frequency, time_of_day,
            export_format, delivery_method, recipients, status, is_enabled,
            last_run_at, next_run_at, last_run_status, run_count,
            success_count, failure_count, created_by_name, created_at
        FROM scheduled_reports
        WHERE 1=1
        """
        params = {}

        if status:
            query += " AND status = :status"
            params['status'] = status

        query += " ORDER BY created_at DESC LIMIT :limit OFFSET :offset"
        params['limit'] = limit
        params['offset'] = offset

        result = db.execute(text(query), params)
        scheduled_reports = []

        for row in result:
            recipients = []
            try:
                if row[8]:  # recipients column
                    recipients = json.loads(row[8]) if isinstance(row[8], str) else row[8]
            except:
                recipients = []

            scheduled_reports.append({
                "id": str(row[0]),
                "name": row[1],
                "description": row[2],
                "reportType": row[3],
                "frequency": row[4],
                "timeOfDay": row[5],
                "exportFormat": row[6],
                "deliveryMethod": row[7],
                "recipients": recipients,
                "status": row[9],
                "isEnabled": row[10],
                "lastRunAt": row[11].isoformat() if row[11] else None,
                "nextRunAt": row[12].isoformat() if row[12] else None,
                "lastRunStatus": row[13],
                "runCount": row[14],
                "successCount": row[15],
                "failureCount": row[16],
                "createdBy": row[17],
                "createdAt": row[18].isoformat() if row[18] else None
            })

        # Get total count
        count_query = "SELECT COUNT(*) FROM scheduled_reports WHERE 1=1"
        count_params = {}

        if status:
            count_query += " AND status = :status"
            count_params['status'] = status

        total_count = db.execute(text(count_query), count_params).scalar()

        return {
            "success": True,
            "data": {
                "scheduledReports": scheduled_reports,
                "pagination": {
                    "total": total_count,
                    "limit": limit,
                    "offset": offset,
                    "hasMore": offset + limit < total_count
                }
            }
        }

    except Exception as e:
        logger.error(f"Error getting scheduled reports: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get scheduled reports: {str(e)}"
        )
