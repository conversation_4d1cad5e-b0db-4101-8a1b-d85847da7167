// Maintenance Service - Frontend API service for maintenance management
// Use relative URL to utilize Vite proxy configuration
const API_BASE_URL = import.meta.env.VITE_API_URL;

export interface Asset {
  id: number;
  asset_id: string;
  type: string;
  model?: string;
  serial_number?: string;
  status: string;
  condition: string;
  location: string;
  assigned_to?: string;
  county?: string;
  precinct?: string;
  purchase_date?: string;
  warranty_expiry?: string;
  last_maintenance?: string;
  next_maintenance?: string;
  last_checked?: string;
  notes?: string;
  specifications?: any;
  created_at: string;
  updated_at: string;
}

export interface AvailableAssetsResponse {
  assets: Asset[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    pages: number;
  };
}

export interface MaintenanceWorkflowRequest {
  asset_tag: string;
  category: 'AV_COMPUTERS' | 'AV_PRINTERS' | 'TABS' | 'OTHERS' | 'SCANNERS' | 'POLLPADS' | 'MONITORS';
  name: string;
  request_type: 'preventive' | 'corrective' | 'emergency';
  priority?: 'low' | 'medium' | 'high' | 'critical';
  assigned_technician?: string;
  description?: string;
  scheduled_date?: string;
  scheduled_frequency?: 'once' | 'weekly' | 'monthly' | 'quarterly' | 'half-yearly' | 'yearly';
  estimated_hours?: number;
  estimated_cost?: number;
  notes?: string;
}

export interface MaintenanceWorkflowResponse {
  message: string;
  maintenance: Maintenance;
  asset_status: string;
  asset?: Asset;
}

export interface MaintenanceCompleteRequest {
  notes?: string;
  assigned_technician?: string;
}

export interface Maintenance {
  assetTag: string;
  name: string;
  category: string;
  subcategory?: string;
  description?: string;
  requestType: 'PREVENTIVE' | 'CORRECTIVE' | 'EMERGENCY' | 'INSPECTION' | 'UPGRADE';
  priority: 'low' | 'medium' | 'high' | 'critical';
  status: 'REQUESTED' | 'SCHEDULED' | 'IN_PROGRESS' | 'TESTING' | 'COMPLETED' | 'CANCELLED' | 'ON_HOLD';
  assignedTechnician?: string;
  requestedBy?: string;
  scheduledDate?: Date;
  completedDate?: Date;
  estimatedHours?: number;
  actualHours?: number;
  estimatedCost?: number;
  actualCost?: number;
  notes?: string;
  workPerformed?: string;
  partsUsed?: string;
  nextMaintenanceDate?: Date;
  warrantyInfo?: string;
  attachments?: string;
  createdAt?: Date;
  updatedAt?: Date;
  // New scheduled frequency fields
  scheduledFrequency?: 'ONCE' | 'WEEKLY' | 'MONTHLY' | 'QUARTERLY' | 'HALF_YEARLY' | 'YEARLY';
  isRecurring?: boolean;
  recurringEndDate?: Date;
  lastRecurrenceDate?: Date;
  nextRecurrenceDate?: Date;
  recurringCount?: number; // How many times this has recurred
  maxRecurrences?: number; // Maximum number of recurrences (-1 for infinite)
}

export interface MaintenanceFilters {
  page?: number;
  limit?: number;
  search?: string;
  category?: string;
  status?: string;
  priority?: string;
  requestType?: string;
  assignedTechnician?: string;
  startDate?: string;
  endDate?: string;
  sortBy?: string;
  sortOrder?: 'ASC' | 'DESC';
  // New frequency filter
  scheduledFrequency?: string;
  isRecurring?: boolean;
}

export interface MaintenanceStats {
  totalRequests: number;
  pendingTasks: number;
  completedLast30Days: number;
  highPriorityTasks: number;
  overdueTasks: number;
  dueThisWeek: number;
  underMaintenanceCount: number;
  underMaintenancePercentage: number;
  statusBreakdown: Array<{ status: string; count: number }>;
  priorityBreakdown: Array<{ priority: string; count: number }>;
  categoryBreakdown: Array<{ category: string; count: number }>;
  requestTypeBreakdown: Array<{ request_type: string; count: number }>;
  // New frequency breakdown
  frequencyBreakdown: Array<{ frequency: string; count: number }>;
  recurringTasksCount: number;
  oneTimeTasksCount: number;
}

export interface AssetStatusOverview {
  status: string;
  count: number;
  description?: string;
}

export interface MaintenanceActivity {
  assetTag: string;
  name: string;
  category: string;
  requestType: string;
  priority: string;
  status: string;
  description?: string;
  assignedTechnician?: string;
  scheduledDate?: Date;
  createdAt: Date;
  updatedAt: Date;
  scheduledFrequency?: string;
  isRecurring?: boolean;
}

export interface RecentActivityResponse {
  recentActivity: MaintenanceActivity[];
  summary: {
    requested: number;
    scheduled: number;
    completed: number;
    total: number;
  };
  activities: {
    requested: MaintenanceActivity[];
    scheduled: MaintenanceActivity[];
    completed: MaintenanceActivity[];
  };
}

export interface DashboardSummary {
  mainMetrics: {
    totalRequests: number;
    pendingTasks: number;
    completedLast30Days: number;
    highPriorityTasks: number;
  };
  additionalMetrics: {
    underMaintenanceCount: number;
    underMaintenancePercentage: number;
    dueThisWeek: number;
    recurringTasksCount: number;
    upcomingRecurringTasks: number;
  };
  assetStatusOverview: AssetStatusOverview[];
  assetSummary: {
    totalAssets: number;
    inMaintenance: number;
    available: number;
    maintenanceRate: number;
  };
}

export interface PaginatedResponse<T> {
  maintenance?: T[];
  data?: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    pages: number;
  };
}

export interface ApiResponse<T> {
  success?: boolean;
  message?: string;
  data?: T;
  error?: string;
}

export interface ScheduleMaintenanceData {
  scheduledDate: string | Date;
  assignedTechnician?: string;
  estimatedHours?: number;
  estimatedCost?: number;
  notes?: string;
  // New frequency scheduling options
  scheduledFrequency?: 'ONCE' | 'WEEKLY' | 'MONTHLY' | 'QUARTERLY' | 'HALF_YEARLY' | 'YEARLY';
  isRecurring?: boolean;
  recurringEndDate?: string | Date;
  maxRecurrences?: number;
}

export interface RecurringMaintenanceOptions {
  frequency: 'WEEKLY' | 'MONTHLY' | 'QUARTERLY' | 'HALF_YEARLY' | 'YEARLY';
  startDate: string | Date;
  endDate?: string | Date;
  maxRecurrences?: number;
  assignedTechnician?: string;
  estimatedHours?: number;
  estimatedCost?: number;
  notes?: string;
}

class MaintenanceService {
  private async makeRequest<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<T> {
    const url = `${API_BASE_URL}/maintenance${endpoint}`;
    
    const defaultOptions: RequestInit = {
      headers: {
        'Content-Type': 'application/json',
        // Add authorization header if token exists
        ...(localStorage.getItem('authToken') && {
          'Authorization': `Bearer ${localStorage.getItem('authToken')}`
        })
      }
    };

    const response = await fetch(url, {
      ...defaultOptions,
      ...options,
      headers: {
        ...defaultOptions.headers,
        ...options.headers
      }
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`HTTP error! status: ${response.status}, message: ${errorText}`);
    }

    return response.json();
  }

  // Get all maintenance records with optional filtering (using dev endpoint)
  async getMaintenanceRecords(filters: MaintenanceFilters = {}): Promise<PaginatedResponse<Maintenance>> {
    const queryParams = new URLSearchParams();
    
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        queryParams.append(key, value.toString());
      }
    });

    const endpoint = queryParams.toString() ? `/dev/list?${queryParams.toString()}` : '/dev/list';
    return this.makeRequest<PaginatedResponse<Maintenance>>(endpoint);
  }

  // Get maintenance statistics (using dev endpoint)
  async getMaintenanceStats(): Promise<MaintenanceStats> {
    return this.makeRequest<MaintenanceStats>('/dev/stats');
  }

  // Get recent maintenance activity (last 10 days)
  async getRecentActivity(): Promise<RecentActivityResponse> {
    return this.makeRequest<RecentActivityResponse>('/recent-activity');
  }

  // Get specific maintenance record by asset tag
  async getMaintenanceByAssetTag(assetTag: string): Promise<Maintenance> {
    return this.makeRequest<Maintenance>(`/${encodeURIComponent(assetTag)}`);
  }

  // Create new maintenance request
  async createMaintenanceRequest(maintenance: Partial<Maintenance>): Promise<Maintenance> {
    return this.makeRequest<Maintenance>('', {
      method: 'POST',
      body: JSON.stringify(maintenance)
    });
  }

  // Update maintenance record
  async updateMaintenance(assetTag: string, maintenance: Partial<Maintenance>): Promise<Maintenance> {
    return this.makeRequest<Maintenance>(`/${encodeURIComponent(assetTag)}`, {
      method: 'PUT',
      body: JSON.stringify(maintenance)
    });
  }

  // Update maintenance status
  async updateMaintenanceStatus(
    assetTag: string, 
    status: Maintenance['status'], 
    notes?: string, 
    assignedTechnician?: string
  ): Promise<Maintenance> {
    const updateData: any = { status };
    if (notes !== undefined) updateData.notes = notes;
    if (assignedTechnician !== undefined) updateData.assignedTechnician = assignedTechnician;

    return this.makeRequest<Maintenance>(`/${encodeURIComponent(assetTag)}/status`, {
      method: 'PUT',
      body: JSON.stringify(updateData)
    });
  }

  // Enhanced schedule maintenance with frequency options
  async scheduleMaintenance(
    assetTag: string,
    scheduleData: ScheduleMaintenanceData
  ): Promise<Maintenance> {
    return this.makeRequest<Maintenance>(`/${encodeURIComponent(assetTag)}/schedule`, {
      method: 'PUT',
      body: JSON.stringify(scheduleData)
    });
  }

  // Create recurring maintenance schedule
  async createRecurringMaintenance(
    assetTag: string,
    recurringOptions: RecurringMaintenanceOptions
  ): Promise<Maintenance> {
    const scheduleData: ScheduleMaintenanceData = {
      scheduledDate: recurringOptions.startDate,
      assignedTechnician: recurringOptions.assignedTechnician,
      estimatedHours: recurringOptions.estimatedHours,
      estimatedCost: recurringOptions.estimatedCost,
      notes: recurringOptions.notes,
      scheduledFrequency: recurringOptions.frequency,
      isRecurring: true,
      recurringEndDate: recurringOptions.endDate,
      maxRecurrences: recurringOptions.maxRecurrences
    };

    return this.makeRequest<Maintenance>(`/${encodeURIComponent(assetTag)}/schedule/recurring`, {
      method: 'POST',
      body: JSON.stringify(scheduleData)
    });
  }

  // Update recurring maintenance settings
  async updateRecurringMaintenance(
    assetTag: string,
    recurringOptions: Partial<RecurringMaintenanceOptions>
  ): Promise<Maintenance> {
    return this.makeRequest<Maintenance>(`/${encodeURIComponent(assetTag)}/schedule/recurring`, {
      method: 'PUT',
      body: JSON.stringify(recurringOptions)
    });
  }

  // Stop recurring maintenance
  async stopRecurringMaintenance(assetTag: string): Promise<Maintenance> {
    return this.makeRequest<Maintenance>(`/${encodeURIComponent(assetTag)}/schedule/recurring/stop`, {
      method: 'PUT'
    });
  }

  // Get all recurring maintenance tasks
  async getRecurringMaintenance(): Promise<Maintenance[]> {
    return this.makeRequest<Maintenance[]>('/recurring/list');
  }

  // Get maintenance by frequency
  async getMaintenanceByFrequency(frequency: Maintenance['scheduledFrequency']): Promise<PaginatedResponse<Maintenance>> {
    return this.getMaintenanceRecords({ scheduledFrequency: frequency });
  }

  // Get upcoming recurring maintenance
  async getUpcomingRecurringMaintenance(days: number = 30): Promise<Maintenance[]> {
    return this.makeRequest<Maintenance[]>(`/recurring/upcoming?days=${days}`);
  }

  // Generate next occurrence for recurring maintenance
  async generateNextRecurrence(assetTag: string): Promise<Maintenance> {
    return this.makeRequest<Maintenance>(`/${encodeURIComponent(assetTag)}/recurring/generate-next`, {
      method: 'POST'
    });
  }

  // Delete maintenance record
  async deleteMaintenance(assetTag: string): Promise<{ message: string }> {
    return this.makeRequest<{ message: string }>(`/${encodeURIComponent(assetTag)}`, {
      method: 'DELETE'
    });
  }

  // Get overdue maintenance tasks
  async getOverdueTasks(): Promise<Maintenance[]> {
    return this.makeRequest<Maintenance[]>('/overdue/list');
  }

  // Get unique categories
  async getCategories(): Promise<string[]> {
    return this.makeRequest<string[]>('/categories/list');
  }

  // Get unique technicians
  async getTechnicians(): Promise<string[]> {
    return this.makeRequest<string[]>('/technicians/list');
  }

  // Get unique frequencies
  async getFrequencies(): Promise<string[]> {
    return this.makeRequest<string[]>('/frequencies/list');
  }

  // Get asset status overview
  async getAssetStatusOverview(): Promise<{
    assetStatusOverview: AssetStatusOverview[];
    detailedBreakdown: {
      totalUniqueAssets: number;
      busyAssets: number;
      overdueAssets: number;
      statusBreakdown: Array<{ status: string; count: number }>;
      priorityBreakdown: Array<{ priority: string; count: number }>;
      frequencyBreakdown: Array<{ frequency: string; count: number }>;
    };
    summary: {
      totalAssets: number;
      inMaintenance: number;
      available: number;
      maintenanceRate: number;
      recurringMaintenanceCount: number;
    };
  }> {
    return this.makeRequest('/asset-status/overview');
  }

  // Get enhanced dashboard summary
  async getDashboardSummary(): Promise<DashboardSummary> {
    return this.makeRequest<DashboardSummary>('/dashboard/summary');
  }

  // Search maintenance records
  async searchMaintenance(query: string, filters: MaintenanceFilters = {}): Promise<PaginatedResponse<Maintenance>> {
    const searchFilters = { ...filters, search: query };
    return this.getMaintenanceRecords(searchFilters);
  }

  // Get maintenance by category
  async getMaintenanceByCategory(category: string): Promise<PaginatedResponse<Maintenance>> {
    return this.getMaintenanceRecords({ category });
  }

  // Get maintenance by status
  async getMaintenanceByStatus(status: Maintenance['status']): Promise<PaginatedResponse<Maintenance>> {
    return this.getMaintenanceRecords({ status });
  }

  // Get maintenance by priority
  async getMaintenanceByPriority(priority: Maintenance['priority']): Promise<PaginatedResponse<Maintenance>> {
    return this.getMaintenanceRecords({ priority });
  }

  // Get maintenance by technician
  async getMaintenanceByTechnician(technician: string): Promise<PaginatedResponse<Maintenance>> {
    return this.getMaintenanceRecords({ assignedTechnician: technician });
  }

  // Get maintenance due this week
  async getMaintenanceDueThisWeek(): Promise<Maintenance[]> {
    const today = new Date();
    const nextWeek = new Date(today.getTime() + 7 * 24 * 60 * 60 * 1000);
    
    const filters: MaintenanceFilters = {
      startDate: today.toISOString().split('T')[0],
      endDate: nextWeek.toISOString().split('T')[0]
    };
    
    const response = await this.getMaintenanceRecords(filters);
    return response.maintenance || response.data || [];
  }

  // Get high priority maintenance
  async getHighPriorityMaintenance(): Promise<Maintenance[]> {
    const criticalResponse = await this.getMaintenanceByPriority('critical');
    const highResponse = await this.getMaintenanceByPriority('high');
    
    const critical = criticalResponse.maintenance || criticalResponse.data || [];
    const high = highResponse.maintenance || highResponse.data || [];
    
    return [...critical, ...high];
  }

  // Complete maintenance task
  async completeMaintenance(
    assetTag: string,
    completionData: {
      actualHours?: number;
      actualCost?: number;
      workPerformed?: string;
      partsUsed?: string;
      nextMaintenanceDate?: string | Date;
      notes?: string;
    }
  ): Promise<Maintenance> {
    const updateData = {
      ...completionData,
      status: 'COMPLETED' as const,
      completedDate: new Date(),
      nextMaintenanceDate: completionData.nextMaintenanceDate
        ? (completionData.nextMaintenanceDate instanceof Date
            ? completionData.nextMaintenanceDate
            : new Date(completionData.nextMaintenanceDate))
        : undefined
    };

    return this.updateMaintenance(assetTag, updateData);
  }

  // Start maintenance work
  async startMaintenance(assetTag: string, assignedTechnician?: string): Promise<Maintenance> {
    const updateData: any = { status: 'IN_PROGRESS' };
    if (assignedTechnician) updateData.assignedTechnician = assignedTechnician;

    return this.updateMaintenanceStatus(assetTag, 'IN_PROGRESS', undefined, assignedTechnician);
  }

  // Cancel maintenance
  async cancelMaintenance(assetTag: string, reason?: string): Promise<Maintenance> {
    return this.updateMaintenanceStatus(assetTag, 'CANCELLED', reason);
  }

  // Put maintenance on hold
  async holdMaintenance(assetTag: string, reason?: string): Promise<Maintenance> {
    return this.updateMaintenanceStatus(assetTag, 'ON_HOLD', reason);
  }

  // Get maintenance summary for dashboard
  async getMaintenanceSummary(): Promise<{
    totalActive: number;
    completedToday: number;
    overdueCount: number;
    highPriorityCount: number;
    recurringTasksCount: number;
    upcomingRecurringTasks: number;
    recentActivity: MaintenanceActivity[];
  }> {
    try {
      const [stats, recentActivity, upcomingRecurring] = await Promise.all([
        this.getMaintenanceStats(),
        this.getRecentActivity(),
        this.getUpcomingRecurringMaintenance(7) // Next 7 days
      ]);

      const today = new Date();
      const todayStr = today.toISOString().split('T')[0];
      
      // Get completed today count from recent activity
      const completedToday = recentActivity.activities.completed.filter(
        activity => activity.updatedAt && 
        new Date(activity.updatedAt).toISOString().split('T')[0] === todayStr
      ).length;

      return {
        totalActive: stats.pendingTasks,
        completedToday,
        overdueCount: stats.overdueTasks,
        highPriorityCount: stats.highPriorityTasks,
        recurringTasksCount: stats.recurringTasksCount,
        upcomingRecurringTasks: upcomingRecurring.length,
        recentActivity: recentActivity.recentActivity.slice(0, 5) // Latest 5 activities
      };
    } catch (error) {
      console.error('Error fetching maintenance summary:', error);
      throw error;
    }
  }

  // Utility method to calculate next maintenance date based on frequency
  calculateNextMaintenanceDate(lastDate: Date, frequency: Maintenance['scheduledFrequency']): Date {
    const nextDate = new Date(lastDate);
    
    switch (frequency) {
      case 'WEEKLY':
        nextDate.setDate(nextDate.getDate() + 7);
        break;
      case 'MONTHLY':
        nextDate.setMonth(nextDate.getMonth() + 1);
        break;
      case 'QUARTERLY':
        nextDate.setMonth(nextDate.getMonth() + 3);
        break;
      case 'HALF_YEARLY':
        nextDate.setMonth(nextDate.getMonth() + 6);
        break;
      case 'YEARLY':
        nextDate.setFullYear(nextDate.getFullYear() + 1);
        break;
      case 'ONCE':
      default:
        return lastDate; // No recurrence for one-time tasks
    }
    
    return nextDate;
  }

  // Get maintenance schedule preview
  async getMaintenanceSchedulePreview(
    assetTag: string,
    frequency: Maintenance['scheduledFrequency'],
    startDate: Date,
    endDate?: Date,
    maxOccurrences?: number
  ): Promise<Date[]> {
    const schedule: Date[] = [];
    let currentDate = new Date(startDate);
    let occurrenceCount = 0;
    
    while (
      (!endDate || currentDate <= endDate) &&
      (!maxOccurrences || occurrenceCount < maxOccurrences) &&
      occurrenceCount < 100 // Safety limit
    ) {
      schedule.push(new Date(currentDate));
      currentDate = this.calculateNextMaintenanceDate(currentDate, frequency);
      occurrenceCount++;
      
      if (frequency === 'ONCE') break;
    }
    
    return schedule;
  }

  // NEW WORKFLOW METHODS

  /**
   * Get assets available for maintenance (status: Ready or Failed) - using dev endpoint
   */
  async getAvailableAssets(filters: {
    page?: number;
    limit?: number;
    search?: string;
    type?: string;
    location?: string;
    county?: string;
  } = {}): Promise<AvailableAssetsResponse> {
    const queryParams = new URLSearchParams();
    
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        queryParams.append(key, value.toString());
      }
    });

    const endpoint = `/dev/available-assets${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
    return this.makeRequest<AvailableAssetsResponse>(endpoint);
  }

  /**
   * Create maintenance request with automatic asset status transition
   */
  async createMaintenanceWorkflowRequest(requestData: MaintenanceWorkflowRequest): Promise<MaintenanceWorkflowResponse> {
    return this.makeRequest<MaintenanceWorkflowResponse>('/request', {
      method: 'POST',
      body: JSON.stringify(requestData)
    });
  }

  /**
   * Complete maintenance and automatically transition asset back to Ready
   */
  async completeMaintenanceWorkflow(assetTag: string, completionData: MaintenanceCompleteRequest): Promise<MaintenanceWorkflowResponse> {
    return this.makeRequest<MaintenanceWorkflowResponse>(`/${assetTag}/complete`, {
      method: 'PUT',
      body: JSON.stringify(completionData)
    });
  }
}

export const maintenanceService = new MaintenanceService();
export default maintenanceService;