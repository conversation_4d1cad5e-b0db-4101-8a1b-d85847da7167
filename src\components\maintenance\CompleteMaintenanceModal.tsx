import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON>alog<PERSON>ontent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { CheckCircle, Loader2, AlertCircle } from 'lucide-react';
import { 
  MaintenanceCompleteRequest, 
  MaintenanceWorkflowResponse, 
  maintenanceService 
} from '@/services/maintenanceService';
import { useToast } from '@/components/ui/use-toast';

interface CompleteMaintenanceModalProps {
  isOpen: boolean;
  onClose: () => void;
  assetTag: string;
  maintenanceName?: string;
  onSuccess?: (result: MaintenanceWorkflowResponse) => void;
}

export const CompleteMaintenanceModal: React.FC<CompleteMaintenanceModalProps> = ({
  isOpen,
  onClose,
  assetTag,
  maintenanceName,
  onSuccess
}) => {
  const { toast } = useToast();
  const [loading, setLoading] = useState(false);
  const [result, setResult] = useState<MaintenanceWorkflowResponse | null>(null);
  const [showConfirmation, setShowConfirmation] = useState(false);

  const [formData, setFormData] = useState<MaintenanceCompleteRequest>({
    notes: '',
    assigned_technician: ''
  });

  const handleSubmit = async () => {
    try {
      setLoading(true);

      const response = await maintenanceService.completeMaintenanceWorkflow(assetTag, formData);
      
      setResult(response);
      setShowConfirmation(true);
      
      toast({
        title: "Success",
        description: `Maintenance completed! Asset ${assetTag} status changed to ${response.asset_status}`,
      });

      if (onSuccess) {
        onSuccess(response);
      }

    } catch (error) {
      console.error('Error completing maintenance:', error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to complete maintenance",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  const handleClose = () => {
    setFormData({ notes: '', assigned_technician: '' });
    setResult(null);
    setShowConfirmation(false);
    onClose();
  };

  const renderCompletionForm = () => (
    <div className="space-y-6">
      <Card className="bg-orange-50 border-orange-200">
        <CardHeader>
          <CardTitle className="text-orange-800">Complete Maintenance</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <span className="font-medium">Asset Tag:</span> {assetTag}
            </div>
            <div>
              <span className="font-medium">Maintenance:</span> {maintenanceName || 'N/A'}
            </div>
          </div>
        </CardContent>
      </Card>

      <div className="space-y-4">
        <div>
          <Label htmlFor="assigned_technician">Technician Completing Work</Label>
          <Input
            id="assigned_technician"
            value={formData.assigned_technician}
            onChange={(e) => setFormData(prev => ({ ...prev, assigned_technician: e.target.value }))}
            placeholder="Technician name..."
          />
        </div>

        <div>
          <Label htmlFor="notes">Completion Notes</Label>
          <Textarea
            id="notes"
            value={formData.notes}
            onChange={(e) => setFormData(prev => ({ ...prev, notes: e.target.value }))}
            placeholder="Describe the work completed, any issues found, parts replaced, etc..."
            rows={4}
          />
        </div>
      </div>

      <div className="bg-blue-50 p-4 rounded-lg">
        <div className="flex items-start space-x-3">
          <AlertCircle className="h-5 w-5 text-blue-600 mt-0.5" />
          <div>
            <h4 className="font-medium text-blue-800 mb-1">What happens next:</h4>
            <ul className="text-sm text-blue-700 space-y-1">
              <li>• Maintenance status will be marked as "COMPLETED"</li>
              <li>• Asset status will automatically change back to "Ready"</li>
              <li>• Asset will be available for normal operations</li>
              <li>• Maintenance history will be recorded</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );

  const renderConfirmation = () => (
    <div className="space-y-6 text-center">
      <div className="flex justify-center">
        <CheckCircle className="h-16 w-16 text-green-600" />
      </div>
      
      <div>
        <h3 className="text-xl font-semibold text-green-800 mb-2">
          Maintenance Completed Successfully!
        </h3>
        <p className="text-gray-600">
          The maintenance has been marked as complete and the asset status has been automatically updated.
        </p>
      </div>

      {result && (
        <Card className="bg-green-50 border-green-200">
          <CardHeader>
            <CardTitle className="text-green-800">Completion Summary</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span className="font-medium">Asset:</span> {result.maintenance.assetTag}
              </div>
              <div>
                <span className="font-medium">New Asset Status:</span>
                <Badge className="ml-2 bg-green-100 text-green-800">{result.asset_status}</Badge>
              </div>
              <div>
                <span className="font-medium">Maintenance Status:</span>
                <Badge className="ml-2 bg-green-100 text-green-800">{result.maintenance.status}</Badge>
              </div>
              <div>
                <span className="font-medium">Completed By:</span> {result.maintenance.assignedTechnician || 'N/A'}
              </div>
            </div>
            
            <div className="bg-white p-3 rounded border">
              <p className="text-green-800 font-medium mb-1">✅ Workflow Status:</p>
              <p className="text-sm text-gray-600">{result.message}</p>
            </div>

            {formData.notes && (
              <div className="bg-white p-3 rounded border">
                <p className="text-gray-800 font-medium mb-1">📝 Completion Notes:</p>
                <p className="text-sm text-gray-600">{formData.notes}</p>
              </div>
            )}
          </CardContent>
        </Card>
      )}

      <div className="bg-green-50 p-4 rounded-lg">
        <h4 className="font-medium text-green-800 mb-2">✅ Workflow Complete:</h4>
        <ul className="text-sm text-green-700 space-y-1">
          <li>• Asset is now ready for normal operations</li>
          <li>• Maintenance record has been completed</li>
          <li>• Asset history has been updated</li>
          <li>• Asset is available for new assignments</li>
        </ul>
      </div>
    </div>
  );

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle>
            {showConfirmation ? 'Maintenance Completed' : 'Complete Maintenance'}
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {showConfirmation ? renderConfirmation() : renderCompletionForm()}
        </div>

        <div className="flex justify-end space-x-3 pt-6 border-t">
          <Button variant="outline" onClick={handleClose}>
            {showConfirmation ? 'Close' : 'Cancel'}
          </Button>

          {!showConfirmation && (
            <Button
              onClick={handleSubmit}
              disabled={loading}
              className="bg-green-600 hover:bg-green-700"
            >
              {loading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Completing...
                </>
              ) : (
                'Complete Maintenance'
              )}
            </Button>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
}; 